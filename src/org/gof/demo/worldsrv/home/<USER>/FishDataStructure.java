package org.gof.demo.worldsrv.home.Fish;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.config.ConfBackTask;
import org.gof.demo.worldsrv.config.ConfFishTask;
import org.gof.demo.worldsrv.entity.HomeFish;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.task.type.TaskVO;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 钓鱼数据结构类
 * 类似AngelData，将HomeFish中的JSON数据转换为对应的数据结构
 */
public class FishDataStructure {
    
    /** 图鉴详情数据 */
    private Map<Integer, FishDetail> albumDetailMap = new HashMap<>();
    
    /** 渔具等级数据 */
    private Map<Integer, Integer> fishTools = new HashMap<>();
    
    /** 鱼塘槽位数据 */
    private Map<Integer, Integer> houseList = new HashMap<>();
    
    /** 每日任务数据 */
    private Map<Integer, TaskVO> dailyTasks = new HashMap<>();

    /** 渔场解锁任务数据 */
    private Map<Integer, TaskVO> fishGroundTasks = new HashMap<>();

    /** 鱼塘装扮数据 */
    private Map<Integer, Integer> houseDesign = new HashMap<>();
    
    /** 自动钓鱼鱼饵数据 */
    private List<List<Integer>> autoFishBaitsSnNum;
    
    /** 会话中破纪录的鱼 */
    private Map<Integer, FishDetail> totalAlbumFishes = new HashMap<>();
    
    /** 会话中卖出的鱼 */
    private Map<Integer, Integer> totalSellFishes = new HashMap<>();
    
    /** 会话中获得的总奖励 */
    private Map<Integer, Long> totalRewards = new HashMap<>();
    
    /**
     * 从HomeFish实体加载数据
     */
    public void loadFromHomeFish(HomeFish homeFish) {
        // 加载图鉴详情
        loadAlbumDetailMap(homeFish.getAlbumDetailMap());
        
        // 加载渔具等级
        loadFishTools(homeFish.getFishTools());
        
        // 加载鱼塘槽位
        loadHouseList(homeFish.getHouseList());

        // 加载鱼塘装备
        loadHouseDesign(homeFish.getHouseDesign());
        
        // 加载每日任务
        loadDailyTasks(homeFish.getDailyTasks());

        // 加载渔场解锁任务
        loadFishGroundTasks(homeFish.getFishGroundTasks());
        
        // 加载自动钓鱼鱼饵
        loadAutoFishBaitsSnNum(homeFish.getAutoFishBaitsSnNum());
        
        // 加载会话数据
        loadTotalAlbumFishes(homeFish.getTotalAlbumFishes());
        loadTotalSellFishes(homeFish.getTotalSellFishes());
        loadTotalRewards(homeFish.getTotalRewards());
    }
    
    /**
     * 保存数据到HomeFish实体
     */
    public void saveToHomeFish(HomeFish homeFish) {
        // 保存图鉴详情
        homeFish.setAlbumDetailMap(saveAlbumDetailMap());
        
        // 保存渔具等级
        homeFish.setFishTools(saveFishTools());
        
        // 保存鱼塘槽位
        homeFish.setHouseList(saveHouseList());
        //
        
        // 保存每日任务
        homeFish.setDailyTasks(saveDailyTasks());

        // 保存渔场解锁任务
        homeFish.setFishGroundTasks(saveFishGroundTasks());

        // 保存鱼塘装扮
        homeFish.setHouseDesign(saveHouseDesign());
        
        // 保存自动钓鱼鱼饵
        homeFish.setAutoFishBaitsSnNum(saveAutoFishBaitsSnNum());
        
        // 保存会话数据
        homeFish.setTotalAlbumFishes(saveTotalAlbumFishes());
        homeFish.setTotalSellFishes(saveTotalSellFishes());
        homeFish.setTotalRewards(saveTotalRewards());
    }

    // ===== 单独保存方法 =====

    /**
     * 只保存图鉴详情数据
     */
    public void saveAlbumDetailMapToHomeFish(HomeFish homeFish) {
        homeFish.setAlbumDetailMap(saveAlbumDetailMap());
    }

    /**
     * 只保存渔具等级数据
     */
    public void saveFishToolsToHomeFish(HomeFish homeFish) {
        homeFish.setFishTools(saveFishTools());
    }

    /**
     * 只保存鱼塘槽位数据
     */
    public void saveHouseListToHomeFish(HomeFish homeFish) {
        homeFish.setHouseList(saveHouseList());
    }

    /**
     * 只保存每日任务数据
     */
    public void saveDailyTasksToHomeFish(HomeFish homeFish) {
        homeFish.setDailyTasks(saveDailyTasks());
    }

    /**
     * 只保存渔场解锁任务数据
     */
    public void saveFishGroundTasksToHomeFish(HomeFish homeFish) {
        homeFish.setFishGroundTasks(saveFishGroundTasks());
    }

    /**
     * 只保存鱼塘装扮数据
     */
    public void saveHouseDesignToHomeFish(HomeFish homeFish) {
        homeFish.setHouseDesign(saveHouseDesign());
    }

    /**
     * 只保存自动钓鱼鱼饵数据
     */
    public void saveAutoFishBaitsSnNumToHomeFish(HomeFish homeFish) {
        homeFish.setAutoFishBaitsSnNum(saveAutoFishBaitsSnNum());
    }

    /**
     * 只保存累积图鉴鱼类数据
     */
    public void saveTotalAlbumFishesToHomeFish(HomeFish homeFish) {
        homeFish.setTotalAlbumFishes(saveTotalAlbumFishes());
    }

    /**
     * 只保存累积卖出鱼类数据
     */
    public void saveTotalSellFishesToHomeFish(HomeFish homeFish) {
        homeFish.setTotalSellFishes(saveTotalSellFishes());
    }

    /**
     * 只保存累积奖励数据
     */
    public void saveTotalRewardsToHomeFish(HomeFish homeFish) {
        homeFish.setTotalRewards(saveTotalRewards());
    }
    
    // ===== 加载方法 =====
    
    private void loadAlbumDetailMap(String json) {
        albumDetailMap.clear();
        if (Utils.isEmptyJSONString(json)) return;
        
        Map<String, Object> map = Utils.jsonToMap(json);
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            int fishGroupSn = Utils.intValue(entry.getKey());
            JSONObject detailJson = (JSONObject) entry.getValue();
            FishDetail detail = detailJson.toJavaObject(FishDetail.class);
            albumDetailMap.put(fishGroupSn, detail);
        }
    }
    
    private void loadFishTools(String json) {
        fishTools.clear();
        if (Utils.isEmptyJSONString(json)) return;
        
        Map<String, Integer> map = Utils.jsonToMapStringInt(json);
        for (Map.Entry<String, Integer> entry : map.entrySet()) {
            int toolType = Utils.intValue(entry.getKey());
            fishTools.put(toolType, entry.getValue());
        }
    }
    
    private void loadHouseList(String json) {
        houseList.clear();
        if (Utils.isEmptyJSONString(json)) return;
        
        Map<String, Object> map = Utils.jsonToMap(json);
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            int locationId = Utils.intValue(entry.getKey());
            int fishGroupSn = Utils.intValue(entry.getValue());
            houseList.put(locationId, fishGroupSn);
        }
    }
    
    private void loadDailyTasks(String json) {
        JSONArray ja = Utils.toJSONArray(json);
        for(int i = 0; i < ja.size(); i++){
            TaskVO vo = new TaskVO(ja.getString(i));
            ConfFishTask conf = ConfFishTask.get(vo.taskSn);
            if(conf == null){
                Log.task.error("===ConfFishTask 配表错误， not find sn={}", vo.taskSn);
                continue;
            }
            vo.setConditionType(conf.condition[0]);
            vo.setParam1(conf.condition[1]);
            vo.setSumPlan(conf.condition[2]);
            dailyTasks.put(vo.taskSn, vo);
        }
    }

    private void loadFishGroundTasks(String json) {
        JSONArray ja = Utils.toJSONArray(json);
        for(int i = 0; i < ja.size(); i++){
            TaskVO vo = new TaskVO(ja.getString(i));
            ConfFishTask conf = ConfFishTask.get(vo.taskSn);
            if(conf == null){
                Log.task.error("===ConfFishTask 配表错误， not find sn={}", vo.taskSn);
                continue;
            }
            vo.setConditionType(conf.condition[0]);
            vo.setParam1(conf.condition[1]);
            vo.setSumPlan(conf.condition[2]);
            fishGroundTasks.put(vo.taskSn, vo);
        }
    }

    private void loadHouseDesign(String json) {
        houseDesign.clear();
        if (Utils.isEmptyJSONString(json)) return;

        Map<String, Object> map = Utils.jsonToMap(json);
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            int designSn = Utils.intValue(entry.getKey());
            int level = Utils.intValue(entry.getValue());
            houseDesign.put(designSn, level);
        }
    }
    
    private void loadAutoFishBaitsSnNum(String json) {
        autoFishBaitsSnNum = null;
        if (Utils.isEmptyJSONString(json)) return;
        
        autoFishBaitsSnNum = JSON.parseObject(
                json,
                new TypeReference<List<List<Integer>>>() {}
        );
    }
    
    private void loadTotalAlbumFishes(String json) {
        totalAlbumFishes.clear();
        if (Utils.isEmptyJSONString(json)) return;
        
        Map<String, Object> map = Utils.jsonToMap(json);
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            int fishGroupSn = Utils.intValue(entry.getKey());
            JSONObject detailJson = (JSONObject) entry.getValue();
            FishDetail detail = detailJson.toJavaObject(FishDetail.class);
            totalAlbumFishes.put(fishGroupSn, detail);
        }
    }
    
    private void loadTotalSellFishes(String json) {
        totalSellFishes.clear();
        if (Utils.isEmptyJSONString(json)) return;
        
        Map<String, Object> map = Utils.jsonToMap(json);
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            int fishGroupSn = Utils.intValue(entry.getKey());
            int count = Utils.intValue(entry.getValue());
            totalSellFishes.put(fishGroupSn, count);
        }
    }
    
    private void loadTotalRewards(String json) {
        totalRewards.clear();
        if (Utils.isEmptyJSONString(json)) return;
        
        Map<String, Object> map = Utils.jsonToMap(json);
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            int currencyId = Utils.intValue(entry.getKey());
            long amount = Utils.longValue(entry.getValue());
            totalRewards.put(currencyId, amount);
        }
    }
    
    // ===== 保存方法 =====
    
    private String saveAlbumDetailMap() {
        if (albumDetailMap.isEmpty()) return "{}";

        Map<String, Object> map = new HashMap<>();
        for (Map.Entry<Integer, FishDetail> entry : albumDetailMap.entrySet()) {
            map.put(String.valueOf(entry.getKey()), entry.getValue());
        }
        return Utils.mapToJSON(map);
    }

    /**
     * 公开的保存图鉴详情方法
     */
    public String saveAlbumDetailMapToJson() {
        return saveAlbumDetailMap();
    }

    /**
     * 公开的保存鱼塘装扮方法
     */
    public String saveHouseDesignToJson() {
        return saveHouseDesign();
    }

    /**
     * 公开的保存鱼塘槽位方法
     */
    public String saveHouseListToJson() {
        return saveHouseList();
    }

    /**
     * 公开的保存每日任务方法
     */
    public String saveDailyTasksToJson() {
        return saveDailyTasks();
    }

    /**
     * 公开的保存渔场解锁任务方法
     */
    public String saveFishGroundTasksToJson() {
        return saveFishGroundTasks();
    }
    
    private String saveFishTools() {
        if (fishTools.isEmpty()) return "{}";
        
        Map<String, Integer> map = new HashMap<>();
        for (Map.Entry<Integer, Integer> entry : fishTools.entrySet()) {
            map.put(String.valueOf(entry.getKey()), entry.getValue());
        }
        return Utils.mapStringIntToJSON(map);
    }
    
    private String saveHouseList() {
        if (houseList.isEmpty()) return "{}";
        
        Map<String, Object> map = new HashMap<>();
        for (Map.Entry<Integer, Integer> entry : houseList.entrySet()) {
            map.put(String.valueOf(entry.getKey()), entry.getValue());
        }
        return Utils.mapToJSON(map);
    }


    
    private String saveDailyTasks() {
        if (dailyTasks.isEmpty()) return "{}";
        JSONArray ja = new JSONArray();
        for(TaskVO vo : dailyTasks.values()){
            ja.add(vo.toString());
        }
        return ja.toJSONString();
    }

    private String saveFishGroundTasks() {
        if (fishGroundTasks.isEmpty()) return "{}";

        Map<String, Object> map = new HashMap<>();
        for (Map.Entry<Integer, TaskVO> entry : fishGroundTasks.entrySet()) {
            map.put(String.valueOf(entry.getKey()), entry.getValue());
        }
        return Utils.mapToJSON(map);
    }

    private String saveHouseDesign() {
        if (houseDesign.isEmpty()) return "{}";

        Map<String, Object> map = new HashMap<>();
        for (Map.Entry<Integer, Integer> entry : houseDesign.entrySet()) {
            map.put(String.valueOf(entry.getKey()), entry.getValue());
        }
        return Utils.mapToJSON(map);
    }
    
    private String saveAutoFishBaitsSnNum() {
        if (autoFishBaitsSnNum == null || autoFishBaitsSnNum.isEmpty()) return "[]";
        return JSON.toJSONString(autoFishBaitsSnNum);
    }
    
    private String saveTotalAlbumFishes() {
        if (totalAlbumFishes.isEmpty()) return "{}";
        
        Map<String, Object> map = new HashMap<>();
        for (Map.Entry<Integer, FishDetail> entry : totalAlbumFishes.entrySet()) {
            map.put(String.valueOf(entry.getKey()), entry.getValue());
        }
        return Utils.mapToJSON(map);
    }
    
    private String saveTotalSellFishes() {
        if (totalSellFishes.isEmpty()) return "{}";
        
        Map<String, Object> map = new HashMap<>();
        for (Map.Entry<Integer, Integer> entry : totalSellFishes.entrySet()) {
            map.put(String.valueOf(entry.getKey()), entry.getValue());
        }
        return Utils.mapToJSON(map);
    }
    
    private String saveTotalRewards() {
        if (totalRewards.isEmpty()) return "{}";
        
        Map<String, Object> map = new HashMap<>();
        for (Map.Entry<Integer, Long> entry : totalRewards.entrySet()) {
            map.put(String.valueOf(entry.getKey()), entry.getValue());
        }
        return Utils.mapToJSON(map);
    }
    
    // ===== Getter/Setter =====
    
    public Map<Integer, FishDetail> getAlbumDetailMap() {
        return albumDetailMap;
    }
    
    public Map<Integer, Integer> getFishTools() {
        return fishTools;
    }
    
    public Map<Integer, Integer> getHouseList() {
        return houseList;
    }
    
    public Map<Integer, TaskVO> getDailyTasks() {
        return dailyTasks;
    }

    public Map<Integer, TaskVO> getFishGroundTasks() {
        return fishGroundTasks;
    }

    public Map<Integer, Integer> getHouseDesign() {
        return houseDesign;
    }
    
    public List<List<Integer>> getAutoFishBaitsSnNum() {
        return autoFishBaitsSnNum;
    }
    
    public void setAutoFishBaitsSnNum(List<List<Integer>> autoFishBaitsSnNum) {
        this.autoFishBaitsSnNum = autoFishBaitsSnNum;
    }
    
    public Map<Integer, FishDetail> getTotalAlbumFishes() {
        return totalAlbumFishes;
    }
    
    public Map<Integer, Integer> getTotalSellFishes() {
        return totalSellFishes;
    }
    
    public Map<Integer, Long> getTotalRewards() {
        return totalRewards;
    }
}
