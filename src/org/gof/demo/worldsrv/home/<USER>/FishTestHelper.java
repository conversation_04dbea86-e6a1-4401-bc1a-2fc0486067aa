package org.gof.demo.worldsrv.home.Fish;

import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.entity.HomeFish;
import org.gof.demo.worldsrv.support.Log;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 钓鱼系统测试辅助类
 */
public class FishTestHelper {
    
    /**
     * 创建测试用的钓鱼数据
     */
    public static void createTestFishData(HumanObject humanObj) {
        if (humanObj.operation.fishData == null) {
            humanObj.operation.fishData = new FishData();
        }
        
        HomeFish homeFish = new HomeFish();
        homeFish.setId(humanObj.id);
        
        // 设置测试数据
        homeFish.setFisherLv(5);
        homeFish.setFisherExp(100);
        homeFish.setAlbumLv(2);
        homeFish.setAlbumExp(50);
        homeFish.setTotalScore(1000);
        homeFish.setMaxUnlockedGround(2);
        
        // 创建测试图鉴数据
        Map<String, Object> albumMap = new HashMap<>();
        FishDetail testFish1 = new FishDetail(1001, 25, 3, 15);
        FishDetail testFish2 = new FishDetail(1002, 30, 2, 8);
        albumMap.put("1", testFish1);
        albumMap.put("2", testFish2);
        homeFish.setAlbumDetailMap(Utils.mapToJSON(albumMap));
        
        // 创建测试渔具数据
        Map<String, Integer> toolMap = new HashMap<>();
        toolMap.put("1", 3); // 渔具类型1，等级3
        toolMap.put("2", 2); // 渔具类型2，等级2
        homeFish.setFishTools(Utils.mapStringIntToJSON(toolMap));
        
        // 创建测试鱼塘数据
        Map<String, Object> houseMap = new HashMap<>();
        houseMap.put("1", 1); // 槽位1装备鱼类组1
        houseMap.put("2", 0); // 槽位2已解锁但未装备
        houseMap.put("3", 2); // 槽位3装备鱼类组2
        homeFish.setHouseList(Utils.mapToJSON(houseMap));
        
        // 其他默认数据
        homeFish.setAlbumScoreRewards("");
        homeFish.setDailyTaskGroupSn(0);
        homeFish.setDailyTasks("{}");
        homeFish.setDailyTaskRecv(false);
        homeFish.setFishGroundTasks("{}");
        
        // 自动钓鱼数据
        homeFish.setAutoFishGroundSn(0);
        homeFish.setAutoFishStartTime(0);
        homeFish.setAutoFishBaitsSnNum("[]");
        homeFish.setAutoFishCastNum(0);
        homeFish.setAutoFishSettleCount(0);
        
        // 统计数据
        homeFish.setTotalAlbumFishes("{}");
        homeFish.setTotalSellFishes("{}");
        homeFish.setTotalCastCnt(50);
        homeFish.setTotalReelSuccCnt(35);
        homeFish.setTotalSlippedCnt(15);
        
        // 总奖励数据
        Map<String, Object> rewardsMap = new HashMap<>();
        rewardsMap.put("1", 1000L); // 货币ID 1，数量1000
        rewardsMap.put("2", 500L);  // 货币ID 2，数量500
        homeFish.setTotalRewards(Utils.mapToJSON(rewardsMap));
        
        humanObj.operation.fishData.setHomeFish(homeFish);
        
        Log.game.info("创建测试钓鱼数据完成, humanId={}", humanObj.id);
    }
    
    /**
     * 打印钓鱼数据信息
     */
    public static void printFishData(HumanObject humanObj) {
        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            Log.game.info("钓鱼数据为空, humanId={}", humanObj.id);
            return;
        }
        
        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        
        Log.game.info("=== 钓鱼数据信息 humanId={} ===", humanObj.id);
        Log.game.info("钓鱼者等级: {}, 经验: {}", homeFish.getFisherLv(), homeFish.getFisherExp());
        Log.game.info("图鉴等级: {}, 经验: {}, 总分: {}", 
            homeFish.getAlbumLv(), homeFish.getAlbumExp(), homeFish.getTotalScore());
        Log.game.info("最高解锁渔场: {}", homeFish.getMaxUnlockedGround());
        
        // 图鉴信息
        if (!Utils.isEmptyJSONString(homeFish.getAlbumDetailMap())) {
            Log.game.info("图鉴详情: {}", homeFish.getAlbumDetailMap());
        }
        
        // 渔具信息
        if (!Utils.isEmptyJSONString(homeFish.getFishTools())) {
            Log.game.info("渔具等级: {}", homeFish.getFishTools());
        }
        
        // 鱼塘信息
        if (!Utils.isEmptyJSONString(homeFish.getHouseList())) {
            Log.game.info("鱼塘装备: {}", homeFish.getHouseList());
        }
        
        // 统计信息
        Log.game.info("钓鱼统计 - 抛竿: {}, 成功: {}, 逃跑: {}", 
            homeFish.getTotalCastCnt(), homeFish.getTotalReelSuccCnt(), homeFish.getTotalSlippedCnt());
        
        // 自动钓鱼状态
        if (homeFish.getAutoFishGroundSn() > 0) {
            Log.game.info("自动钓鱼中 - 渔场: {}, 开始时间: {}, 抛竿数: {}", 
                homeFish.getAutoFishGroundSn(), homeFish.getAutoFishStartTime(), homeFish.getAutoFishCastNum());
        }
        
        Log.game.info("=== 钓鱼数据信息结束 ===");
    }
    
    /**
     * 测试钓鱼信息查询
     */
    public static void testFishInfo(HumanObject humanObj) {
        Log.game.info("测试钓鱼信息查询, humanId={}", humanObj.id);
        
        try {
            HomeFishManager.inst().handleFishDataC2S(humanObj);
            Log.game.info("钓鱼信息查询测试成功");
        } catch (Exception e) {
            Log.game.error("钓鱼信息查询测试失败", e);
        }
    }
    
    /**
     * 测试抛竿钓鱼
     */
    public static void testFishCast(HumanObject humanObj, int groundSn, List<Integer> baitSnList, int castNum) {
        Log.game.info("测试抛竿钓鱼, humanId={}, groundSn={}, baitSn={}, castNum={}", 
            humanObj.id, groundSn, baitSnList, castNum);
        
        try {
            HomeFishManager.inst().handleFishCastC2S(humanObj, groundSn, baitSnList, castNum);
            Log.game.info("抛竿钓鱼测试成功");
        } catch (Exception e) {
            Log.game.error("抛竿钓鱼测试失败", e);
        }
    }
    
    /**
     * 测试收竿结算
     */
    public static void testFishReel(HumanObject humanObj, int state) {
        Log.game.info("测试收竿结算, humanId={}, state={}", humanObj.id, state);
        
        try {
            HomeFishManager.inst().handleFishReelC2S(humanObj, state);
            Log.game.info("收竿结算测试成功");
        } catch (Exception e) {
            Log.game.error("收竿结算测试失败", e);
        }
    }
    
    /**
     * 测试鱼类升级
     */
    public static void testFishLevelUp(HumanObject humanObj, List<Integer> fishTypeSnList) {
        Log.game.info("测试鱼类升级, humanId={}, fishTypeSn={}", humanObj.id, fishTypeSnList);
        
        try {
//            HomeFishManager.inst().handleFishGroupLevelUpC2S(humanObj, fishTypeSnList);
            Log.game.info("鱼类升级测试成功");
        } catch (Exception e) {
            Log.game.error("鱼类升级测试失败", e);
        }
    }
    
    /**
     * 测试渔具升级
     */
    public static void testToolUpgrade(HumanObject humanObj, int toolType) {
        Log.game.info("测试渔具升级, humanId={}, toolType={}", humanObj.id, toolType);
        
        try {
            HomeFishManager.inst().handleFishToolLvUpC2S(humanObj, toolType);
            Log.game.info("渔具升级测试成功");
        } catch (Exception e) {
            Log.game.error("渔具升级测试失败", e);
        }
    }
    
    /**
     * 运行完整测试流程
     */
    public static void runFullTest(HumanObject humanObj) {
        Log.game.info("开始钓鱼系统完整测试, humanId={}", humanObj.id);
        
        // 1. 创建测试数据
        createTestFishData(humanObj);
        
        // 2. 打印初始数据
        printFishData(humanObj);
        
        // 3. 测试各个功能
        testFishInfo(humanObj);
//        testFishCast(humanObj, 1, 1, 1);
        testFishReel(humanObj, 1);
//        testFishLevelUp(humanObj, 1);
        testToolUpgrade(humanObj, 1);
        
        // 4. 打印最终数据
        printFishData(humanObj);
        
        Log.game.info("钓鱼系统完整测试结束, humanId={}", humanObj.id);
    }
}
