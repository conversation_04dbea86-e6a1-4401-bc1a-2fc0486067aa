package org.gof.demo.worldsrv.home.Fish;

public class EFishType {
    //FishConfig_钓鱼配置表的sn
    public static final int CONF_FISH_BASE_SPEED = 1; // 指示器小鱼基础游动速度
    public static final int CONF_FISH_SPEED_LIMITS = 2; // 指示器小鱼游动速度下限 | 上限
    public static final int CONF_AREA_BASE_WIDTH = 3; // 指示器有效区基础宽度
    public static final int CONF_AREA_WIDTH_RANGE = 4; // 指示器有效区宽度范围
    public static final int CONF_FISH_BASE_PROGRESS = 5; // 钓鱼所需基础进度 (毫秒)
    public static final int CONF_FISH_PROGRESS_RANGE = 6; // 钓鱼所需进度范围
    public static final int CONF_BASE_ESCAPE_RATE = 7; // 基础逃跑概率 (万分比)
    public static final int CONF_ESCAPE_RATE_RANGE = 8; // 逃跑概率范围
    public static final int CONF_TURN_INTERVAL_RANGE = 9; // 转向间隔随机范围 (毫秒)
    public static final int CONF_GUIDE_ANIM_INTERVAL = 10; // 指引动画间隔 (毫秒)
    public static final int CONF_FORCE_FAIL_TIME = 11; // 强制失败时间 (毫秒)
    public static final int CONF_AREA_MOVE_SPEED = 12; // 钓鱼有效区移动速度
    public static final int CONF_AUTO_FISH_INTERVAL = 13; // 自动钓鱼间隔 (秒)
    public static final int CONF_FISH_EXP_REWARD = 14; // 每次成功钓鱼提供战令经验
    public static final int CONF_DAILY_EXP_LIMIT = 15; // 每日战令经验上限
    public static final int CONF_FISH_10_CAST_LV = 16; // 10连抛竿开启等级

    // ===== 鱼饵类型常量 =====

    // ===== 鱼类型常量 =====
    public static final int FISH_TYPE_PRECIOUS = 5; // 珍贵鱼类型
    public static final int FISH_TYPE_COMMON = 2; // 普通鱼类型

    // ===== 渔场类型常量 =====
    public static final int GROUND_TYPE_PERMANENT = 1; // 常驻渔场
    public static final int GROUND_TYPE_LIMITED = 2; // 限时渔场


    // ===== 默认方案ID =====
    public static final int DEFAULT_HOUSE_TAB = 1; // 默认鱼塘方案ID

    //FishBait_鱼饵表的sn
    public static final int BAIT_NORMAL = 1; // 普通鱼饵
    public static final int BAIT_SMALL = 2; // 小型鱼饵: 加成小型鱼概率
    public static final int BAIT_MEDIUM = 3; // 中型鱼饵: 加成中型鱼概率
    public static final int BAIT_LARGE = 4; // 大型鱼饵: 加成大型鱼概率
    public static final int BAIT_AQUATIC = 5; // 水生鱼饵: 加成水中生物概率
    public static final int BAIT_PRECIOUS = 6; // 珍贵鱼饵: 加成珍贵鱼概率
    public static final int BAIT_RARE = 7; // 稀有鱼饵: 加成更稀有的鱼概率
    public static final int BAIT_LENGTH = 8; // 长度鱼饵: 加成更长的鱼概率
    public static final int BAIT_LOSSLESS = 9; // 无损鱼饵: 概率不消耗鱼饵
    public static final int BAIT_FISH_KING = 10; // 鱼王鱼饵: 加成鱼王概率

    /*
     * 渔具类型
     */
    public static final int FISHING_ROD = 1;//鱼竿
    public static final int FISHING_LINE = 2;//鱼钩
    public static final int FISHING_HOOK = 3;//鱼钩

}
