package org.gof.demo.worldsrv.home.Fish;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import org.gof.core.Port;
import org.gof.core.support.ManagerBase;
import org.gof.core.support.Param;
import org.gof.core.support.Time;
import org.gof.core.support.Utils;
import org.gof.core.support.observer.Listener;
import org.gof.demo.battlesrv.manager.CombatChangeLog;
import org.gof.demo.battlesrv.manager.PropManager;
import org.gof.demo.battlesrv.support.PropCalc;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.entity.HomeFish;
import org.gof.demo.worldsrv.entity.UnitPropPlus;
import org.gof.demo.worldsrv.human.EModule;
import org.gof.demo.worldsrv.human.FuncOpenType;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.item.ItemManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgHome;
import org.gof.demo.worldsrv.msg.MsgTask;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.ReasonResult;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.support.observer.EventKey;
import org.gof.demo.worldsrv.task.TaskConditionTypeKey;
import org.gof.demo.worldsrv.task.type.TaskVO;

import java.util.*;
import java.util.ArrayList;
import java.util.Collections;


public class HomeFishManager extends ManagerBase {

    /**
     * 获取实例
     *
     * @return
     */
    public static HomeFishManager inst() {
        return inst(HomeFishManager.class);
    }

    /**
     * 功能解锁与初始化
     * 监听全局功能开启事件
     */
    @Listener(EventKey.FUNCTION_OPEN)
    public void onFunctionOpen(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if(humanObj.operation.fishData != null && humanObj.operation.fishData.getHomeFish() != null){
            return;
        }
        if(!humanObj.isModUnlock(FuncOpenType.FUNC_HOME_FISH)){
            return;
        }
        createFishData(humanObj);
    }

    /**
     * 初始化钓鱼数据
     */
    public void createFishData(HumanObject humanObj) {
        // 创建FishData实例
        if (humanObj.operation.fishData == null) {
            humanObj.operation.fishData = new FishData();
        }

        // 创建HomeFish实体
        HomeFish homeFish = new HomeFish();
        homeFish.setId(humanObj.id);

        // 设置初始数据
        homeFish.setFisherLv(1);
        homeFish.setFisherExp(0);
        homeFish.setAlbumLv(0);
        homeFish.setAlbumExp(0);
        homeFish.setTotalScore(0);
        homeFish.setAlbumDetailMap("{}");
        homeFish.setFishTools("{}");
        homeFish.setHouseList("{}");
        homeFish.setAlbumScoreRewards("");
        homeFish.setDailyTaskGroupSn(0);
        homeFish.setDailyTasks("{}");
        homeFish.setDailyTaskRecv(false);
        homeFish.setMaxUnlockedGround(1); // 解锁默认渔场

        humanObj.operation.fishData.setHomeFish(homeFish);

        // 解锁默认鱼塘槽位
        unlockDefaultHouseSlots(humanObj);

        //解锁渔具
        unlockDefaultTools(humanObj);

        // 初始化下一渔场任务
        initNextGroundTasks(humanObj);

        // 保存数据
        homeFish.persist();

        Log.fish.debug("初始化钓鱼数据完成, humanId={}", humanObj.id);
    }

    /**
     * 解锁默认鱼塘槽位
     */
    private void unlockDefaultHouseSlots(HumanObject humanObj) {
        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        Map<String, Object> houseMap = Utils.jsonToMap(homeFish.getHouseList());

        // 获取所有鱼塘配置
        Collection<ConfFishHouse> houseConfigs = ConfFishHouse.findAll();
        for (ConfFishHouse config : houseConfigs) {
            // 如果解锁不消耗道具，就默认解锁这个槽位
            if (config.cost == null || config.cost.length == 0 ||
                (config.cost.length == 2 && config.cost[1] == 0)) {
                String key = String.valueOf(config.sn);
                houseMap.put(key, 0); // 0表示已解锁但未装备
            }
        }

        homeFish.setHouseList(Utils.mapToJSON(houseMap));
    }

    private void unlockDefaultTools(HumanObject humanObj) {
        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        Map<Integer, Integer> toolMap = Utils.jsonToMapIntInt(homeFish.getFishTools());
        toolMap.put(EFishType.FISHING_ROD, 1);
        toolMap.put(EFishType.FISHING_LINE, 1);
        toolMap.put(EFishType.FISHING_HOOK, 1);
        homeFish.setFishTools(Utils.mapIntIntToJSON(toolMap));
    }

    /**
     * 初始化下一渔场任务
     */
    private void initNextGroundTasks(HumanObject humanObj) {
        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        int maxUnlockedGround = homeFish.getMaxUnlockedGround();

        // 读取当前最高解锁渔场的配置
        ConfFishGround currentGround = ConfFishGround.get(maxUnlockedGround);
        if (currentGround != null && currentGround.unlock_ground > 0) {
            // 获取下一个要解锁的渔场配置
            ConfFishGround nextGround = ConfFishGround.get(currentGround.unlock_ground);
            if (nextGround != null && nextGround.unlock != null) {
                // 生成解锁任务 - 这里需要调用通用任务系统
                for (int taskSn : nextGround.unlock) {
                    ConfFishTask conf = ConfFishTask.get(taskSn);
                    if(conf == null){
                        Log.fish.error("===ConfFishTask 配表错误， not find sn={}", taskSn);
                        continue;
                    }
                    TaskVO taskVO = new TaskVO(taskSn, TaskConditionTypeKey.TASK_渔场解锁, conf.condition, null, 0);
                    humanObj.operation.fishData.getDataStructure().getFishGroundTasks().put(taskSn, taskVO);
                    humanObj.operation.fishData.getDataStructure().saveFishGroundTasksToJson();
                }
                TaskVO taskVO = new TaskVO();
//                taskVO.setTaskId(nextGround.unlock);
                // TODO: 实现任务系统集成
                Log.fish.debug("初始化渔场解锁任务, humanId={}, nextGroundSn={}",
                    humanObj.id, nextGround.sn);
            }
        }
    }

    /**
     * 处理钓鱼数据查询请求
     */
    public void handleFishDataC2S(HumanObject humanObj) {
        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            Log.fish.error("钓鱼数据未初始化, humanId={}", humanObj.id);
            return;
        }

        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        FishDataStructure dataStructure = humanObj.operation.fishData.getDataStructure();

        // 构建响应消息
        MsgHome.home_fish_data_s2c.Builder builder = MsgHome.home_fish_data_s2c.newBuilder();

        // 基础信息
        builder.setFisherLv(homeFish.getFisherLv());
        builder.setFisherExp(homeFish.getFisherExp());
        builder.setTotalScore((int)homeFish.getTotalScore());
        builder.setAlbumLv(homeFish.getAlbumLv());
        builder.setAlbumExp(homeFish.getAlbumExp());
        builder.setMaxUnlockFishGround(homeFish.getMaxUnlockedGround());

        // 图鉴详情
        Map<Integer, FishDetail> albumDetailMap = dataStructure.getAlbumDetailMap();
        for (Map.Entry<Integer, FishDetail> entry : albumDetailMap.entrySet()) {
            FishDetail detail = entry.getValue();
            Define.p_home_fish_detail.Builder detailBuilder = Define.p_home_fish_detail.newBuilder();
            detailBuilder.setFishTypeSn(entry.getKey());
            detailBuilder.setFishSn(detail.getFishSn());
            detailBuilder.setLen(detail.getMaxLen());
            detailBuilder.setLv(detail.getLevel());
            detailBuilder.setExp(detail.getExp());
            builder.addAlbumDetail(detailBuilder.build());
        }

        // 渔具等级
        Map<Integer, Integer> fishTools = dataStructure.getFishTools();
        for (Map.Entry<Integer, Integer> entry : fishTools.entrySet()) {
            if (entry.getValue() > 0) {
                Define.p_key_value.Builder kvBuilder = Define.p_key_value.newBuilder();
                kvBuilder.setK(entry.getKey());
                kvBuilder.setV(entry.getValue());
                builder.addFishTool(kvBuilder.build());
            }
        }

        // 每日任务信息
        builder.setDailyTaskGroupSn(homeFish.getDailyTaskGroupSn());
        builder.setIsDailyTaskRecv(homeFish.isDailyTaskRecv());

        // 每日任务列表
        Map<Integer, TaskVO> dailyTasks = dataStructure.getDailyTasks();
        for (TaskVO task : dailyTasks.values()) {
            Define.p_task.Builder taskBuilder = Define.p_task.newBuilder();
            taskBuilder.setTaskId(task.getTaskSn());
            taskBuilder.setState(task.getStatus());
            taskBuilder.setCount(task.getPlan());
            taskBuilder.setType(task.getType());
            builder.addDailyTasks(taskBuilder.build());
        }

        // 渔场解锁任务列表
        Map<Integer, TaskVO> fishGroundTasks = dataStructure.getFishGroundTasks();
        for (TaskVO task : fishGroundTasks.values()) {
            Define.p_task.Builder taskBuilder = Define.p_task.newBuilder();
            taskBuilder.setTaskId(task.getTaskSn());
            taskBuilder.setState(task.getStatus());
            taskBuilder.setCount(task.getPlan());
            taskBuilder.setType(task.getType());
            builder.addFishGroundTasks(taskBuilder.build());
        }

        // 鱼塘信息
        Map<Integer, Integer> houseList = dataStructure.getHouseList();
        for (Map.Entry<Integer, Integer> entry : houseList.entrySet()) {
            Define.p_key_value.Builder kvBuilder = Define.p_key_value.newBuilder();
            kvBuilder.setK(entry.getKey());
            kvBuilder.setV(entry.getValue());
            builder.addHouseList(kvBuilder.build());
        }

        // 鱼塘装扮信息
        Map<Integer, Integer> houseDesign = dataStructure.getHouseDesign();
        for (Map.Entry<Integer, Integer> entry : houseDesign.entrySet()) {
            if (entry.getValue() > 0) { // 只发送已激活的装扮
                Define.p_key_value.Builder kvBuilder = Define.p_key_value.newBuilder();
                kvBuilder.setK(entry.getKey());
                kvBuilder.setV(entry.getValue());
                builder.addHouseDesign(kvBuilder.build());
            }
        }

        // 当前使用的装扮
        builder.setUseDesignSn(homeFish.getUseHouseDesign());

        // 自动钓鱼状态
        if (homeFish.getAutoFishGroundSn() > 0) {
            builder.setAutoStartTime((int)(homeFish.getAutoFishStartTime() / 1000));
            builder.setAutoGroundSn(homeFish.getAutoFishGroundSn());
            builder.setAutoMultiple(homeFish.getAutoFishCastNum());

            // 自动钓鱼鱼饵列表
            List<List<Integer>> autoFishBaitsSnNum = dataStructure.getAutoFishBaitsSnNum();
            if (autoFishBaitsSnNum != null) {
                for (List<Integer> baitInfo : autoFishBaitsSnNum) {
                    if (baitInfo.size() >= 2) {
                        Define.p_key_value.Builder kvBuilder = Define.p_key_value.newBuilder();
                        kvBuilder.setK(baitInfo.get(0)); // 鱼饵SN
                        kvBuilder.setV(baitInfo.get(1)); // 数量
                        builder.addBaitSnNum(kvBuilder.build());
                    }
                }
            }
        }

        // 已领取的图鉴评分奖励
        String albumScoreRewards = homeFish.getAlbumScoreRewards();
        if (!Utils.isEmptyJSONString(albumScoreRewards)) {
            String[] rewardIds = albumScoreRewards.split(",");
            for (String rewardId : rewardIds) {
                builder.addAlbumRewardedList(Utils.intValue(rewardId));
            }
        }

        humanObj.sendMsg(builder.build());
    }

    /**
     * 处理领取总评分奖励请求
     */
    public void handleGetAlbumRewardC2S(HumanObject humanObj, int scoreTaskSn) {
        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            Log.fish.error("钓鱼数据未初始化, humanId={}", humanObj.id);
            return;
        }

        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();

        // 检查是否已领取
        String albumScoreRewards = homeFish.getAlbumScoreRewards();
        if (albumScoreRewards.contains(String.valueOf(scoreTaskSn))) {
            Log.fish.warn("总评分奖励已领取, humanId={}, scoreTaskSn={}", humanObj.id, scoreTaskSn);
            return;
        }

        // TODO: 检查总评分是否达到要求
        // TODO: 发放奖励

        // 记录已领取
        if (Utils.isEmptyJSONString(albumScoreRewards)) {
            albumScoreRewards = String.valueOf(scoreTaskSn);
        } else {
            albumScoreRewards += "," + scoreTaskSn;
        }
        homeFish.setAlbumScoreRewards(albumScoreRewards);
        homeFish.update();

        // 响应客户端
        MsgHome.home_fish_get_album_reward_s2c.Builder builder = MsgHome.home_fish_get_album_reward_s2c.newBuilder();
        String[] rewardIds = albumScoreRewards.split(",");
        for (String rewardId : rewardIds) {
            builder.addAlbumRewardedList(Utils.intValue(rewardId));
        }
        humanObj.sendMsg(builder.build());

        Log.fish.debug("领取总评分奖励成功, humanId={}, scoreTaskSn={}", humanObj.id, scoreTaskSn);
    }

    /**
     * 处理领取每日任务奖励请求
     */
    public void handleDailyTaskRewardC2S(HumanObject humanObj) {
        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            Log.fish.error("钓鱼数据未初始化, humanId={}", humanObj.id);
            return;
        }

        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();

        // 检查是否已领取
        if (homeFish.isDailyTaskRecv()) {
            Log.fish.warn("每日任务奖励已领取, humanId={}", humanObj.id);
            return;
        }

        // TODO: 检查每日任务是否完成
        // TODO: 发放奖励

        // 标记已领取
        homeFish.setDailyTaskRecv(true);
        homeFish.update();

        // 响应客户端
        MsgHome.home_fish_daily_task_reward_s2c.Builder builder = MsgHome.home_fish_daily_task_reward_s2c.newBuilder();
        builder.setResult(0); // 0:成功
        humanObj.sendMsg(builder.build());

        Log.fish.debug("领取每日任务奖励成功, humanId={}", humanObj.id);
    }

    /**
     * 处理渔场解锁请求
     */
    public void handleGroundUnlockC2S(HumanObject humanObj, int fishGroundSn) {
        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            Log.fish.error("钓鱼数据未初始化, humanId={}", humanObj.id);
            return;
        }

        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        FishDataStructure dataStructure = humanObj.operation.fishData.getDataStructure();

        // 检查是否已解锁
        if (homeFish.getMaxUnlockedGround() >= fishGroundSn) {
            Log.fish.warn("渔场已解锁, humanId={}, fishGroundSn={}", humanObj.id, fishGroundSn);
            return;
        }

        // TODO: 检查解锁条件和消耗

        // 解锁渔场
        homeFish.setMaxUnlockedGround(fishGroundSn);

        // 生成下一个渔场的解锁任务
        generateNextGroundTasks(humanObj, fishGroundSn);

        homeFish.update();
        humanObj.operation.fishData.saveDataStructure();

        // 响应客户端
        MsgHome.home_fish_ground_unlock_s2c.Builder builder = MsgHome.home_fish_ground_unlock_s2c.newBuilder();
        builder.setFishGroundSn(fishGroundSn);

        // 添加新的解锁任务
        Map<Integer, TaskVO> fishGroundTasks = dataStructure.getFishGroundTasks();
        for (TaskVO task : fishGroundTasks.values()) {
            Define.p_task.Builder taskBuilder = Define.p_task.newBuilder();
            taskBuilder.setTaskId(task.getTaskSn());
            taskBuilder.setState(task.getStatus());
            taskBuilder.setCount(task.getPlan());
            taskBuilder.setType(task.getType());
            builder.addList(taskBuilder.build());
        }

        humanObj.sendMsg(builder.build());

        Log.fish.debug("渔场解锁成功, humanId={}, fishGroundSn={}", humanObj.id, fishGroundSn);
    }

    /**
     * 处理鱼塘装扮激活升级请求
     */
    public void handleHouseDesignLevelUpC2S(HumanObject humanObj, int designSn) {
        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            Log.fish.error("钓鱼数据未初始化, humanId={}", humanObj.id);
            return;
        }

        FishDataStructure dataStructure = humanObj.operation.fishData.getDataStructure();
        Map<Integer, Integer> houseDesign = dataStructure.getHouseDesign();

        int currentLevel = houseDesign.getOrDefault(designSn, 0);
        int nextLevel = currentLevel + 1;

        // TODO: 检查升级条件和消耗
        // TODO: 扣除货币

        // 执行升级
        houseDesign.put(designSn, nextLevel);

        // 保存数据
        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        homeFish.setHouseDesign(dataStructure.saveHouseDesignToJson());
        homeFish.update();

        // 响应客户端
        MsgHome.home_fish_house_design_level_up_s2c.Builder builder = MsgHome.home_fish_house_design_level_up_s2c.newBuilder();
        builder.setSn(designSn);
        builder.setLevel(nextLevel);
        humanObj.sendMsg(builder.build());

        Log.fish.debug("鱼塘装扮升级成功, humanId={}, designSn={}, newLevel={}",
            humanObj.id, designSn, nextLevel);
    }

    /**
     * 处理设置使用装扮请求
     */
    public void handleSetUseDesignC2S(HumanObject humanObj, int designSn) {
        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            Log.fish.error("钓鱼数据未初始化, humanId={}", humanObj.id);
            return;
        }

        FishDataStructure dataStructure = humanObj.operation.fishData.getDataStructure();
        Map<Integer, Integer> houseDesign = dataStructure.getHouseDesign();

        // 检查装扮是否已激活
        if (designSn > 0 && !houseDesign.containsKey(designSn)) {
            Log.fish.warn("装扮未激活, humanId={}, designSn={}", humanObj.id, designSn);
            return;
        }

        // 设置使用的装扮
        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        homeFish.setUseHouseDesign(designSn);
        homeFish.update();

        // 响应客户端
        MsgHome.home_fish_house_set_use_design_s2c.Builder builder = MsgHome.home_fish_house_set_use_design_s2c.newBuilder();
        builder.setSn(designSn);
        humanObj.sendMsg(builder.build());

        Log.fish.debug("设置使用装扮成功, humanId={}, designSn={}", humanObj.id, designSn);
    }

    /**
     * 处理一键装备请求
     */
    public void handleFishHouseEquipOneClickC2S(HumanObject humanObj, List<Define.p_key_value> equipList) {
        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            Log.fish.error("钓鱼数据未初始化, humanId={}", humanObj.id);
            return;
        }

        FishDataStructure dataStructure = humanObj.operation.fishData.getDataStructure();
        Map<Integer, Integer> houseList = dataStructure.getHouseList();
        Map<Integer, FishDetail> albumDetailMap = dataStructure.getAlbumDetailMap();

        // 验证所有装备请求
        for (Define.p_key_value equip : equipList) {
            int locationId = (int)equip.getK();
            int fishGroupSn = (int)equip.getV();

            // 检查槽位是否已解锁
            if (!houseList.containsKey(locationId)) {
                Log.fish.warn("槽位未解锁, humanId={}, locationId={}", humanObj.id, locationId);
                return;
            }

            // 检查鱼类是否拥有
            if (fishGroupSn > 0 && !albumDetailMap.containsKey(fishGroupSn)) {
                Log.fish.warn("玩家未拥有该鱼类, humanId={}, fishGroupSn={}", humanObj.id, fishGroupSn);
                return;
            }
        }

        // 执行装备
        for (Define.p_key_value equip : equipList) {
            houseList.put((int)equip.getK(), (int)equip.getV());
        }

        // 保存数据
        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        homeFish.setHouseList(dataStructure.saveHouseListToJson());
        homeFish.update();

        // 响应客户端
        MsgHome.home_fish_house_equip_one_click_s2c.Builder builder = MsgHome.home_fish_house_equip_one_click_s2c.newBuilder();
        builder.addAllEquipList(equipList);
        builder.setResult(0); // 0:成功
        humanObj.sendMsg(builder.build());

        Log.fish.debug("一键装备成功, humanId={}, equipCount={}", humanObj.id, equipList.size());
    }

    /**
     * 处理抛竿钓鱼请求
     */
    public void handleFishCastC2S(HumanObject humanObj, int groundSn, List<Integer> baitSnNumList, int castNum) {
        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            Log.fish.error("钓鱼数据未初始化, humanId={}", humanObj.id);
            return;
        }

        // 前置验证
        if (!validateFishCast(humanObj, groundSn, baitSnNumList, castNum)) {
            return;
        }

        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        FishData fishData = humanObj.operation.fishData;

        // 清空旧缓存
        fishData.clearPendingCache();

        // 设置新的缓存数据
        fishData.setPendingGroundSn(groundSn);
        fishData.setPendingCastNum(castNum);

        // 准备鱼饵列表
        List<List<Integer>> baitList = new ArrayList<>();
        for (int baitSn : baitSnNumList) {
            ConfFishBait baitConfig = ConfFishBait.get(baitSn);
            int itemSn = baitConfig.item_id;
            int baitNum = ItemManager.inst().getItemNum(humanObj, itemSn);
            baitList.add(Arrays.asList(baitSn, baitNum));
        }
        fishData.setPendingBaits(baitList);

        // 执行第一次钓鱼逻辑，获取第一条鱼
        int baitSn = baitList.get(0).get(0);
        int firstFishSn = executeFishing(humanObj, groundSn, baitSn);
        if (firstFishSn <= 0) {
            Log.fish.error("钓鱼失败，无法获取鱼类, humanId={}, groundSn={}, baitSn={}",
                humanObj.id, groundSn, baitSn);
            return;
        }

        // 缓存第一条鱼
        fishData.getPendingFishSns().add(firstFishSn);

        // 如果是多次抛竿，继续生成其他鱼
        for (int i = 1; i < castNum; i++) {
            int fishSn = executeFishing(humanObj, groundSn, baitSn);
            if (fishSn > 0) {
                fishData.getPendingFishSns().add(fishSn);
            }
        }

        // 更新抛竿统计
        homeFish.setTotalCastCnt(homeFish.getTotalCastCnt() + castNum);

        // 响应客户端
        MsgHome.home_fish_cast_s2c.Builder builder = MsgHome.home_fish_cast_s2c.newBuilder();
        builder.setFishId(firstFishSn); // 用于客户端表现
        humanObj.sendMsg(builder.build());

        Log.fish.debug("抛竿成功, humanId={}, groundSn={}, baitSn={}, castNum={}, firstFishSn={}",
            humanObj.id, groundSn, baitSn, castNum, firstFishSn);
    }

    /**
     * 验证抛竿请求
     */
    private boolean validateFishCast(HumanObject humanObj, int groundSn, List<Integer> baitSnNumList, int castNum) {
        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();

        // 检查是否正在自动钓鱼
        if (homeFish.getAutoFishGroundSn() > 0) {
            Log.fish.warn("玩家正在自动钓鱼，无法手动抛竿, humanId={}", humanObj.id);
            return false;
        }

        // 检查抛竿次数
        if (castNum != 1 && castNum != 10) {
            Log.fish.warn("无效的抛竿次数, humanId={}, castNum={}", humanObj.id, castNum);
            return false;
        }

        // 检查10连抛竿权限
        if (castNum == 10) {
            ConfFishConfig config = ConfFishConfig.get(EFishType.CONF_FISH_10_CAST_LV);
            if (config != null && config.parameter != null && config.parameter.length > 0) {
                int requiredLv = config.parameter[0][0];
                if (homeFish.getFisherLv() < requiredLv) {
                    Log.fish.warn("钓鱼等级不足，无法10连抛竿, humanId={}, currentLv={}, requiredLv={}",
                        humanObj.id, homeFish.getFisherLv(), requiredLv);
                    return false;
                }
            }
        }

        // 检查鱼饵数量
        // TODO: 实现道具系统检查

        for (int baitSn : baitSnNumList) {
            ConfFishBait baitConfig = ConfFishBait.get(baitSn);
            int itemSn = baitConfig.item_id;
            int baitNum = ItemManager.inst().getItemNum(humanObj, itemSn);
            if (baitNum <= 0) {
                Log.fish.warn("鱼饵数量不足, humanId={}, baitSn={}, baitNum={}, realBaitNum={}",
                    humanObj.id, baitSn, baitNum, baitNum);
                return false;
            }
            // 检查渔场是否可用该鱼饵
            ConfFishGround groundConfig = ConfFishGround.get(groundSn);
            if (groundConfig == null) {
                Log.fish.warn("渔场配置不存在, humanId={}, groundSn={}", humanObj.id, groundSn);
                return false;
            }

            if (groundConfig.usable_bait != null) {
                boolean baitAllowed = false;
                for (int allowedBait : groundConfig.usable_bait) {
                    if (allowedBait == baitSn) {
                        baitAllowed = true;
                        break;
                    }
                }
                if (!baitAllowed) {
                    Log.fish.warn("该渔场不允许使用此鱼饵, humanId={}, groundSn={}, baitSn={}",
                            humanObj.id, groundSn, baitSn);
                    return false;
                }
            }
        }


        return true;
    }

    /**
     * 执行钓鱼逻辑 - 随机生成鱼类
     */
    private int executeFishing(HumanObject humanObj, int groundSn, int baitSn) {
        // A. 随机鱼类组 (Fish Group)
        int fishGroupId = randomFishGroup(humanObj, groundSn, baitSn);
        if (fishGroupId <= 0) {
            return 0;
        }

        // B. 随机长度级别 (grade)
        int fishSn = randomFishGrade(humanObj, fishGroupId);

        return fishSn;
    }

    /**
     * 随机鱼类组
     */
    private int randomFishGroup(HumanObject humanObj, int groundSn, int baitSn) {
        ConfFishGround groundConfig = ConfFishGround.get(groundSn);
        if (groundConfig == null || groundConfig.fish_show == null) {
            return 0;
        }

        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        FishDataStructure dataStructure = humanObj.operation.fishData.getDataStructure();

        // 获取渔具属性
        Map<String, Integer> toolAttrs = getToolAttributes(dataStructure);
        int toolLucky = toolAttrs.getOrDefault("lucky", 0);
        int toolLiveFactor = toolAttrs.getOrDefault("live_factor", 0);

        // 获取鱼饵属性
        ConfFishBait baitConfig = ConfFishBait.get(baitSn);

        // 获取月卡加成 - TODO: 实现月卡系统集成
        int monthCardBonus = 0;

        // 计算各鱼类组的最终权重
        List<Integer> weights = new ArrayList<>();
        List<Integer> groupIds = new ArrayList<>();

        for (int[] fishShow : groundConfig.fish_show) {
            if (fishShow.length < 2) continue;

            int groupId = fishShow[0];
            int baseWeight = fishShow[1];

            ConfFishGroup groupConfig = ConfFishGroup.get(groupId);
            if (groupConfig == null) continue;

            int finalWeight = calculateFishGroupWeight(groupConfig, baseWeight,
                toolLucky, toolLiveFactor, baitConfig, monthCardBonus);

            weights.add(finalWeight);
            groupIds.add(groupId);
        }

        // 根据权重随机选择
        int selectedIndex = Utils.getRandRange(weights);
        if (selectedIndex >= 0 && selectedIndex < groupIds.size()) {
            return groupIds.get(selectedIndex);
        }

        return 0;
    }

    /**
     * 计算鱼类组权重
     */
    private int calculateFishGroupWeight(ConfFishGroup groupConfig, int baseWeight,
                                       int toolLucky, int toolLiveFactor,
                                       ConfFishBait baitConfig, int monthCardBonus) {

        int finalWeight = baseWeight;

        if (groupConfig.type == EFishType.FISH_TYPE_PRECIOUS) {
            // 珍贵鱼最终权重 = 基础权重 * [1 + 渔具幸运 * (1 + 月卡加成*0.01) * 0.01] + 鱼饵加成权重
            double multiplier = 1.0 + toolLucky * (1.0 + monthCardBonus * 0.01) * 0.01;
            finalWeight = (int)(baseWeight * multiplier);

            // 添加鱼饵加成
            if (baitConfig != null && baitConfig.parameter != null && baitConfig.parameter.length > 0) {
                finalWeight += baitConfig.parameter[0]; // 珍贵鱼饵加成
            }
        } else {
            // 其他鱼类组最终权重 = 基础权重 * (1 + 渔具权重加成*0.01) + 鱼饵权重加成
            double multiplier = 1.0 + toolLiveFactor * 0.01;
            finalWeight = (int)(baseWeight * multiplier);

            // 添加鱼饵加成 - 根据鱼类型匹配
            if (baitConfig != null && baitConfig.parameter != null && baitConfig.parameter.length > 0) {
                finalWeight += baitConfig.parameter[0]; // 普通鱼饵加成
            }
        }

        return Math.max(finalWeight, 1); // 确保权重至少为1
    }

    /**
     * 随机长度级别
     */
    private int randomFishGrade(HumanObject humanObj, int fishGroupId) {
        FishDataStructure dataStructure = humanObj.operation.fishData.getDataStructure();

        // 获取玩家渔具总等级
        int totalToolLevel = getTotalToolLevel(dataStructure);

        // 筛选符合条件的鱼类基础配置
        Collection<ConfFishBase> allFishBase = ConfFishBase.findAll();
        List<ConfFishBase> validFish = new ArrayList<>();

        for (ConfFishBase fishBase : allFishBase) {
            if (fishBase.fish_group_id == fishGroupId && fishBase.need_tool <= totalToolLevel) {
                validFish.add(fishBase);
            }
        }

        if (validFish.isEmpty()) {
            return 0;
        }

        // 获取渔具丰饵属性
        Map<String, Integer> toolAttrs = getToolAttributes(dataStructure);
        int sizeFactor = toolAttrs.getOrDefault("size_factor", 0);

        // 计算权重
        List<Integer> weights = new ArrayList<>();
        List<Integer> fishSns = new ArrayList<>();

        for (ConfFishBase fishBase : validFish) {
            int finalWeight = fishBase.weight + sizeFactor;

            // TODO: 添加鱼饵长度加成

            weights.add(Math.max(finalWeight, 1));
            fishSns.add(fishBase.sn);
        }

        // 根据权重随机选择
        int selectedIndex = Utils.getRandRange(weights);
        if (selectedIndex >= 0 && selectedIndex < fishSns.size()) {
            return fishSns.get(selectedIndex);
        }

        return 0;
    }

    /**
     * 获取渔具属性总和
     */
    private Map<String, Integer> getToolAttributes(FishDataStructure dataStructure) {
        Map<String, Integer> attrs = new HashMap<>();
        attrs.put("stable", 0);
        attrs.put("strength", 0);
        attrs.put("focus", 0);
        attrs.put("efficiency", 0);
        attrs.put("size_factor", 0);
        attrs.put("live_factor", 0);
        attrs.put("lucky", 0);

        Map<Integer, Integer> toolMap = dataStructure.getFishTools();

        for (Map.Entry<Integer, Integer> entry : toolMap.entrySet()) {
            int toolType = entry.getKey();
            int toolLevel = entry.getValue();

            ConfFishTool toolConfig = ConfFishTool.get(toolType, toolLevel);

            if (toolConfig != null) {
                attrs.put("stable", attrs.get("stable") + (int)toolConfig.getFieldValue(ConfFishTool.K.stable));
                attrs.put("strength", attrs.get("strength") + (int)toolConfig.getFieldValue(ConfFishTool.K.strength));
                attrs.put("focus", attrs.get("focus") + (int)toolConfig.getFieldValue(ConfFishTool.K.focus));
                attrs.put("efficiency", attrs.get("efficiency") + (int)toolConfig.getFieldValue(ConfFishTool.K.efficiency));
                attrs.put("size_factor", attrs.get("size_factor") + (int)toolConfig.getFieldValue(ConfFishTool.K.size_factor));
                attrs.put("live_factor", attrs.get("live_factor") + (int)toolConfig.getFieldValue(ConfFishTool.K.live_factor));
                attrs.put("lucky", attrs.get("lucky") + (int)toolConfig.getFieldValue(ConfFishTool.K.lucky));
            }
        }

        return attrs;
    }

    /**
     * 获取渔具总等级
     */
    private int getTotalToolLevel(FishDataStructure dataStructure) {
        Map<Integer, Integer> toolMap = dataStructure.getFishTools();
        int totalLevel = 0;

        for (Integer level : toolMap.values()) {
            totalLevel += level;
        }

        return totalLevel;
    }

    /**
     * 处理收竿结算请求
     */
    public void handleFishReelC2S(HumanObject humanObj, int state) {
        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            Log.fish.error("钓鱼数据未初始化, humanId={}", humanObj.id);
            return;
        }

        // 只处理成功的情况，失败直接返回
        if (state != 0) {
            humanObj.operation.fishData.clearPendingCache();
            Log.fish.debug("收竿失败，直接返回, humanId={}", humanObj.id);
            return;
        }

        FishData fishData = humanObj.operation.fishData;
        HomeFish homeFish = fishData.getHomeFish();

        // 获取待结算的鱼
        List<Integer> pendingFishSns = new ArrayList<>(fishData.getPendingFishSns());
        List<List<Integer>> pendingBaits = new ArrayList<>(fishData.getPendingBaits());
        int castNum = fishData.getPendingCastNum();

        // 清空缓存
        fishData.clearPendingCache();

        if (pendingFishSns.isEmpty()) {
            Log.fish.warn("没有待结算的鱼, humanId={}", humanObj.id);
            return;
        }

        // 初始化结算数据
        int reelSuccCnt = 0;
        int slippedCnt = 0;
        Map<Integer, Integer> rewardMap = new HashMap<>();
        List<FishDetail> albumFishesUpdate = new ArrayList<>();
        Map<Integer, Integer> sellFishes = new HashMap<>();
        int firstFishId = 0;
        int firstFishLen = 0;

        // 消耗鱼饵
        if(!consumeBaits(humanObj, pendingBaits, castNum)){
            return;
        }

        // 循环结算每一条鱼
        for (int i = 0; i < pendingFishSns.size(); i++) {
            int fishSn = pendingFishSns.get(i);
            ConfFishBase fishBase = ConfFishBase.get(fishSn);
            if (fishBase == null) {
                continue;
            }

            // 权威逃跑判定
            if (calculateEscapeRate(humanObj, fishBase)) {
                slippedCnt++;
                continue;
            }

            // 成功钓到
            reelSuccCnt++;
            int randomLen = Utils.random(fishBase.len_range[0], fishBase.len_range[1]);

            // 记录第一条鱼的信息
            if (firstFishId == 0 && castNum == 1) {
                firstFishId = fishSn;
                firstFishLen = randomLen;
            }

            // A. 奖励发放
            processRewards(humanObj, fishBase, rewardMap);

            // B. 图鉴数据更新
            FishDetail updatedFish = updateAlbumData(humanObj, fishBase, randomLen);
            if (updatedFish != null) {
                albumFishesUpdate.add(updatedFish);
            }

            // C. 统计卖出的鱼
            ConfFishGroup groupConfig = ConfFishGroup.get(fishBase.fish_group_id);
            if (groupConfig != null) {
                sellFishes.put(fishBase.fish_group_id,
                    sellFishes.getOrDefault(fishBase.fish_group_id, 0) + 1);
            }
        }
        ProduceManager.inst().produceAdd(humanObj, rewardMap, MoneyItemLogKey.钓鱼收竿);
        // 更新统计数据
        homeFish.setTotalReelSuccCnt(homeFish.getTotalReelSuccCnt() + reelSuccCnt);
        homeFish.setTotalSlippedCnt(homeFish.getTotalSlippedCnt() + slippedCnt);


        // 响应客户端
        sendFishReelResponse(humanObj, fishData.getPendingCastNum(), reelSuccCnt, slippedCnt,
            rewardMap, albumFishesUpdate, sellFishes, firstFishId, firstFishLen);

        // 更新任务进度
        updateFishTasks(humanObj, EFishTaskCondition.FISH_SUCCESS_COUNT, reelSuccCnt);
        updateFishTasks(humanObj, EFishTaskCondition.FISH_COUNT, fishData.getPendingCastNum());

        Log.fish.debug("收竿结算完成, humanId={}, 成功={}, 逃跑={}",
            humanObj.id, reelSuccCnt, slippedCnt);
    }

    /**
     * 消耗鱼饵
     */
    private boolean consumeBaits(HumanObject humanObj, List<List<Integer>> baitSnNums, int num) {
        List<int[]> baitList = new ArrayList<>();
        int remainingNum = num;

        for (List<Integer> baitSnNum : baitSnNums) {
            if (remainingNum <= 0) {
                break;
            }

            int baitSn = baitSnNum.get(0);
            int baitNum = baitSnNum.get(1);

            ConfFishBait baitConfig = ConfFishBait.get(baitSn);
            if (baitConfig == null) {
                Log.fish.error("鱼饵配置不存在, humanId={}, baitSn={}", humanObj.id, baitSn);
                continue;
            }

            int useBaitNum = Math.min(baitNum, remainingNum);
            int actualConsumeNum = useBaitNum;

            // 检查是否是概率不消耗的鱼饵
            if (baitConfig.sn == EFishType.BAIT_LOSSLESS &&
                    baitConfig.parameter != null && baitConfig.parameter.length > 0) {

                int probability = baitConfig.parameter[0];
                for (int i = 0; i < useBaitNum; i++) {
                    if (Utils.random(10000) <= probability) {
                        actualConsumeNum--; // 概率不消耗
                    }
                }
                actualConsumeNum = Math.max(0, actualConsumeNum); // 确保不小于0
            }

            if (actualConsumeNum > 0) {
                baitList.add(new int[]{baitConfig.item_id, actualConsumeNum});
            }

            remainingNum -= useBaitNum;
            Log.fish.debug("消耗鱼饵, humanId={}, baitSn={}, 使用数量={}, 实际消耗={}",
                    humanObj.id, baitSn, useBaitNum, actualConsumeNum);
        }

        // 转换为二维数组
        int[][] baits = baitList.toArray(new int[0][]);

        // 鱼饵转道具消耗
        return ProduceManager.inst().checkAndCostItem(humanObj, baits, MoneyItemLogKey.钓鱼收竿).success;
    }

    /**
     * 计算逃跑率
     */
    private boolean calculateEscapeRate(HumanObject humanObj, ConfFishBase fishBase) {
        FishDataStructure dataStructure = humanObj.operation.fishData.getDataStructure();

        // 获取基础逃跑率配置
        ConfFishConfig escapeConfig = ConfFishConfig.get(EFishType.CONF_BASE_ESCAPE_RATE);
        if (escapeConfig == null || escapeConfig.parameter == null || escapeConfig.parameter.length == 0) {
            return false;
        }

        int baseEscapeRate = escapeConfig.parameter[0][0]; // 万分比

        // 获取渔具稳固属性
        Map<String, Integer> toolAttrs = getToolAttributes(dataStructure);
        int toolStable = toolAttrs.getOrDefault("stable", 0);

        // 获取小鱼稳固属性
        int fishStable = fishBase.restable;

        // 计算最终逃跑率: 基础逃跑率*0.0001/[(1+渔具稳固*0.01)*(1-小鱼稳固*0.01)]
        double escapeRate = (baseEscapeRate * 0.0001) /
            ((1.0 + toolStable * 0.01) * (1.0 - fishStable * 0.01));

        // 转换为万分比
        int finalEscapeRate = (int)(escapeRate * 10000);

        return Utils.random(10000) < finalEscapeRate;
    }

    /**
     * 处理奖励发放
     */
    private void processRewards(HumanObject humanObj, ConfFishBase fishBase, Map<Integer, Integer> rewardMap) {
        ConfFishGroup groupConfig = ConfFishGroup.get(fishBase.fish_group_id);
        if (groupConfig == null) return;

        // 发放货币奖励
        if (groupConfig.sell != null && groupConfig.sell.length >= 2) {
            int currencyId = groupConfig.sell[0];
            int amount = groupConfig.sell[1];

            rewardMap.put(currencyId, rewardMap.getOrDefault(currencyId, 0) + amount);
        }

        // 发放钓鱼经验
        if (groupConfig.exp > 0) {
            addFisherExp(humanObj, groupConfig.exp);
        }
    }

    /**
     * 更新图鉴数据
     */
    private FishDetail updateAlbumData(HumanObject humanObj, ConfFishBase fishBase, int randomLen) {
        FishDataStructure dataStructure = humanObj.operation.fishData.getDataStructure();

        // 获取当前图鉴数据
        Map<Integer, FishDetail> albumMap = dataStructure.getAlbumDetailMap();
        int fishGroupId = fishBase.fish_group_id;

        FishDetail currentDetail = albumMap.get(fishGroupId);

        boolean isNewRecord = false;

        if (currentDetail == null) {
            // 第一次钓到这种鱼
            currentDetail = new FishDetail(fishBase.sn, randomLen, 0, 1);
            isNewRecord = true;
        } else {
            // 检查是否破纪录
            if (randomLen > currentDetail.getMaxLen()) {
                currentDetail.setMaxLen(randomLen);
                currentDetail.setFishSn(fishBase.sn); // 更新为新的鱼类基础表sn
                isNewRecord = true;
            }

            // 累积经验
            currentDetail.setExp(currentDetail.getExp() + 1);
        }

        // 更新图鉴
        albumMap.put(fishGroupId, currentDetail);
        // 保存数据结构到实体
        humanObj.operation.fishData.saveDataStructure();

        // 如果破纪录，需要更新属性
        if (isNewRecord) {
            // TODO: 调用属性计算系统
            Log.fish.debug("鱼类破纪录, humanId={}, fishGroupId={}, newLen={}",
                humanObj.id, fishBase.fish_group_id, randomLen);
        }

        return currentDetail;
    }

    /**
     * 添加钓鱼者经验
     */
    public void addFisherExp(HumanObject humanObj, int exp) {
        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();

        int currentExp = homeFish.getFisherExp() + exp;
        homeFish.setFisherExp(currentExp);

        // 检查是否可以升级
        checkFisherLevelUp(humanObj);
    }

    /**
     * 检查钓鱼者等级提升
     */
    private void checkFisherLevelUp(HumanObject humanObj) {
        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();

        while (true) {
            // TODO: 读取Fisher_钓鱼等级表配置
            ConfFisher fisherConfig = ConfFisher.get(homeFish.getFisherLv());
            // 这里需要配置表支持，暂时使用简单的升级逻辑
            int nextLevel = homeFish.getFisherLv() + 1;
            int needExp = fisherConfig.need; // 简单的升级公式

            if (homeFish.getFisherExp() >= needExp) {
                homeFish.setFisherLv(nextLevel);
                homeFish.setFisherExp(homeFish.getFisherExp() - needExp);

                // TODO: 派发等级提升事件
                Log.fish.debug("钓鱼者升级, humanId={}, newLevel={}", humanObj.id, nextLevel);
            } else {
                break;
            }
        }
    }

    /**
     * 更新总奖励统计
     */
    private void updateTotalRewards(HomeFish homeFish, Map<Integer, Integer> rewardMap) {
        Map<String, Object> totalRewardsMap = Utils.jsonToMap(homeFish.getTotalRewards());

        for (Map.Entry<Integer, Integer> entry : rewardMap.entrySet()) {
            String currencyKey = String.valueOf(entry.getKey());
            long currentAmount = Utils.longValue(totalRewardsMap.get(currencyKey));
            totalRewardsMap.put(currencyKey, currentAmount + entry.getValue());
        }

        homeFish.setTotalRewards(Utils.mapToJSON(totalRewardsMap));
    }

    /**
     * 发送收竿响应
     */
    private void sendFishReelResponse(HumanObject humanObj, int castNum, int reelSuccCnt, int slippedCnt,
                                    Map<Integer, Integer> rewardMap, List<FishDetail> albumFishesUpdate,
                                    Map<Integer, Integer> sellFishes, int firstFishId, int firstFishLen) {

        MsgHome.home_fish_reel_s2c.Builder builder = MsgHome.home_fish_reel_s2c.newBuilder();

        // 图鉴更新的鱼
        for (FishDetail detail : albumFishesUpdate) {
            Define.p_home_fish_detail.Builder detailBuilder = Define.p_home_fish_detail.newBuilder();
            detailBuilder.setFishTypeSn(detail.getFishSn()); // 这里需要鱼类组SN
            detailBuilder.setFishSn(detail.getFishSn());
            detailBuilder.setLen(detail.getMaxLen());
            detailBuilder.setLv(detail.getLevel());
            detailBuilder.setExp(detail.getExp());
            builder.addAlbumFishes(detailBuilder.build());
        }

        // 卖出的鱼
        for (Map.Entry<Integer, Integer> entry : sellFishes.entrySet()) {
            Define.p_key_value.Builder kvBuilder = Define.p_key_value.newBuilder();
            kvBuilder.setK(entry.getKey());
            kvBuilder.setV(entry.getValue());
            builder.addSellFishes(kvBuilder.build());
        }

        builder.setCastNum(castNum);
        builder.setReelSuccCnt(reelSuccCnt);
        builder.setSlippedCnt(slippedCnt);

        // 出售获得的奖励
        for (Map.Entry<Integer, Integer> entry : rewardMap.entrySet()) {
            Define.p_reward.Builder rewardBuilder = Define.p_reward.newBuilder();
            rewardBuilder.setGtid(entry.getKey());
            rewardBuilder.setNum(entry.getValue());
            builder.addReward(rewardBuilder.build());
        }

        builder.setFishId(firstFishId);
        builder.setLen(firstFishLen);

        humanObj.sendMsg(builder.build());
    }

    /**
     * 处理鱼类升级请求
     */
    public void handleFishGroupLevelUpC2S(HumanObject humanObj, int fishType) {
        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            Log.fish.error("钓鱼数据未初始化, humanId={}", humanObj.id);
            return;
        }

        FishDataStructure dataStructure = humanObj.operation.fishData.getDataStructure();
        Map<Integer, FishDetail> albumDetailMap = dataStructure.getAlbumDetailMap();

        List<FishUpgradeInfo> upgradeInfoList = new ArrayList<>();
        List<Integer> upgradedFishTypes = new ArrayList<>();

        if (fishType == 0) {
            // 一键升级所有鱼（按ID排序）
            List<Integer> sortedFishTypes = new ArrayList<>(albumDetailMap.keySet());
            Collections.sort(sortedFishTypes);

            Log.fish.info("开始一键升级, humanId={}, 鱼类数量={}, 排序后ID={}",
                humanObj.id, sortedFishTypes.size(), sortedFishTypes);

            for (Integer fishTypeSn : sortedFishTypes) {
                FishDetail detail = albumDetailMap.get(fishTypeSn);
                if (detail != null) {
                    Log.fish.debug("尝试升级鱼类, fishTypeSn={}, currentLevel={}, currentExp={}",
                        fishTypeSn, detail.getLevel(), detail.getExp());

                    FishUpgradeInfo upgradeInfo = upgradeToMaxLevel(humanObj, fishTypeSn, detail);
                    if (upgradeInfo != null) {
                        upgradeInfoList.add(upgradeInfo);
                        upgradedFishTypes.add(fishTypeSn);
                        Log.fish.info("鱼类升级成功, fishTypeSn={}, {}级→{}级, 消耗货币={}",
                            fishTypeSn, upgradeInfo.oldLevel, upgradeInfo.newLevel, upgradeInfo.costAmount);
                    } else {
                        Log.fish.debug("鱼类无法升级, fishTypeSn={}", fishTypeSn);
                    }
                }
            }
        } else {
            // 升级指定鱼类一级
            FishDetail detail = albumDetailMap.get(fishType);
            if (detail != null) {
                Log.fish.debug("尝试升级指定鱼类一级, fishTypeSn={}, currentLevel={}, currentExp={}",
                    fishType, detail.getLevel(), detail.getExp());

                FishUpgradeInfo upgradeInfo = upgradeOneLevel(humanObj, fishType, detail);
                if (upgradeInfo != null) {
                    upgradeInfoList.add(upgradeInfo);
                    upgradedFishTypes.add(fishType);
                    Log.fish.info("指定鱼类升级成功, fishTypeSn={}, {}级→{}级, 消耗货币={}",
                        fishType, upgradeInfo.oldLevel, upgradeInfo.newLevel, upgradeInfo.costAmount);
                } else {
                    Log.fish.debug("指定鱼类无法升级, fishTypeSn={}", fishType);
                }
            } else {
                Log.fish.warn("指定鱼类不存在, fishTypeSn={}", fishType);
            }
        }

        // 执行升级
        boolean hasUpgrade = false;
        for (FishUpgradeInfo upgradeInfo : upgradeInfoList) {
            FishDetail detail = albumDetailMap.get(upgradeInfo.fishTypeSn);
            if (detail != null) {
                detail.setLevel(upgradeInfo.newLevel);
                detail.setExp(upgradeInfo.remainExp);
                hasUpgrade = true;
            }
        }

        if (hasUpgrade) {
            // 只保存图鉴数据
            HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
            homeFish.setAlbumDetailMap(dataStructure.saveAlbumDetailMapToJson());

            // 重新计算评分
            updateTotalScore(humanObj);

            // 检查图鉴升级
            checkAlbumLevelUp(humanObj);
        }

        // 响应客户端
        sendFishGroupLevelUpResponse(humanObj, upgradedFishTypes, upgradeInfoList);

        Log.fish.debug("鱼类升级完成, humanId={}, upgradeCount={}", humanObj.id, upgradeInfoList.size());
    }

    /**
     * 升级指定鱼类一级
     */
    private FishUpgradeInfo upgradeOneLevel(HumanObject humanObj, int fishTypeSn, FishDetail detail) {
        int currentLevel = detail.getLevel();
        int currentExp = detail.getExp();

        // 检查是否可以升级
        ConfFishLevel nextLevelConfig = ConfFishLevel.get(fishTypeSn, currentLevel + 1);
        if (nextLevelConfig == null) {
            Log.fish.debug("鱼类已达最大等级, fishTypeSn={}, currentLevel={}", fishTypeSn, currentLevel);
            return null;
        }
        ConfFishLevel currentLevelConfig = ConfFishLevel.get(fishTypeSn, currentLevel);
        // 检查经验是否足够
        if (currentExp < currentLevelConfig.need) {
            Log.fish.debug("经验不足，无法升级, fishTypeSn={}, currentExp={}, needExp={}",
                fishTypeSn, currentExp, nextLevelConfig.need);
            return null;
        }

        // 检查并消耗货币
        if (currentLevelConfig.cost != null && currentLevelConfig.cost.length >= 2) {
            int currencyId = currentLevelConfig.cost[0];
            int costAmount = currentLevelConfig.cost[1];

            ReasonResult result = ProduceManager.inst().checkAndCostItem(humanObj, currencyId, costAmount,
                MoneyItemLogKey.钓鱼图鉴升级);

            if (!result.success) {
                Log.fish.warn("货币不足，无法升级鱼类, humanId={}, fishTypeSn={}, currencyId={}, needAmount={}",
                    humanObj.id, fishTypeSn, currencyId, costAmount);
                return null;
            }
        }

        // 创建升级信息
        int newLevel = currentLevel + 1;
        int remainExp = currentExp - currentLevelConfig.need;

        return new FishUpgradeInfo(fishTypeSn, currentLevel, newLevel, remainExp,
                currentLevelConfig.cost != null ? currentLevelConfig.cost[0] : 0,
                currentLevelConfig.cost != null ? currentLevelConfig.cost[1] : 0);
    }

    /**
     * 升级指定鱼类到最大等级（一键升级用）
     * 一级一级升级，货币不足就停止，继续下一个鱼类
     */
    private FishUpgradeInfo upgradeToMaxLevel(HumanObject humanObj, int fishTypeSn, FishDetail detail) {
        int currentLevel = detail.getLevel();
        int currentExp = detail.getExp();
        int newLevel = currentLevel;
        int remainExp = currentExp;

        long totalCostAmount = 0;
        int mainCurrencyId = 0;

        // 一级一级升级，直到货币不足或经验不足
        while (true) {
            ConfFishLevel nextLevelConfig = ConfFishLevel.get(fishTypeSn, newLevel + 1);
            if (nextLevelConfig == null) {
                Log.fish.debug("鱼类已达最大等级, fishTypeSn={}, level={}", fishTypeSn, newLevel);
                break; // 已达最大等级
            }
            ConfFishLevel currentLevelConfig = ConfFishLevel.get(fishTypeSn, newLevel);

            // 检查经验是否足够
            if (remainExp < currentLevelConfig.need) {
                Log.fish.debug("经验不足，停止升级, fishTypeSn={}, level={}, remainExp={}, needExp={}",
                    fishTypeSn, newLevel, remainExp, nextLevelConfig.need);
                break; // 经验不足
            }

            // 检查并消耗本级升级的货币
            boolean canUpgrade = true;
            if (currentLevelConfig.cost != null && currentLevelConfig.cost.length >= 2) {
                int currencyId = currentLevelConfig.cost[0];
                int costAmount = currentLevelConfig.cost[1];

                ReasonResult result = ProduceManager.inst().checkAndCostItem(humanObj, currencyId, costAmount,
                    MoneyItemLogKey.钓鱼图鉴升级);

                if (!result.success) {
                    Log.fish.debug("货币不足，停止升级, fishTypeSn={}, level={}, currencyId={}, needAmount={}",
                        fishTypeSn, newLevel, currencyId, costAmount);
                    canUpgrade = false;
                } else {
                    // 记录消耗
                    totalCostAmount += costAmount;
                    mainCurrencyId = currencyId;
                }
            }

            if (!canUpgrade) {
                break; // 货币不足，停止升级
            }

            // 执行升级
            newLevel++;
            remainExp -= currentLevelConfig.need;

            Log.fish.debug("鱼类升级成功, fishTypeSn={}, newLevel={}, remainExp={}",
                fishTypeSn, newLevel, remainExp);
        }

        // 如果没有升级，返回null
        if (newLevel == currentLevel) {
            Log.fish.debug("鱼类无法升级, fishTypeSn={}, currentLevel={}", fishTypeSn, currentLevel);
            return null;
        }

        // 创建升级信息
        return new FishUpgradeInfo(fishTypeSn, currentLevel, newLevel, remainExp,
            mainCurrencyId, totalCostAmount);
    }

    /**
     * 发送鱼类升级响应
     */
    private void sendFishGroupLevelUpResponse(HumanObject humanObj, List<Integer> upgradedFishTypes,
                                            List<FishUpgradeInfo> upgradeInfoList) {
        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        FishDataStructure dataStructure = humanObj.operation.fishData.getDataStructure();

        MsgHome.home_fish_group_level_up_s2c.Builder builder = MsgHome.home_fish_group_level_up_s2c.newBuilder();

        // 设置总评分和图鉴等级
        builder.setTotalScore((int)homeFish.getTotalScore());
        builder.setAlbumLv(homeFish.getAlbumLv());
        builder.setAlbumExp(homeFish.getAlbumExp());

        // 添加变更的图鉴详情
        Map<Integer, FishDetail> albumDetailMap = dataStructure.getAlbumDetailMap();
        for (Integer fishTypeSn : upgradedFishTypes) {
            FishDetail detail = albumDetailMap.get(fishTypeSn);
            if (detail != null) {
                Define.p_home_fish_detail.Builder detailBuilder = Define.p_home_fish_detail.newBuilder();
                detailBuilder.setFishTypeSn(fishTypeSn);
                detailBuilder.setFishSn(detail.getFishSn());
                detailBuilder.setLen(detail.getMaxLen());
                detailBuilder.setLv(detail.getLevel());
                detailBuilder.setExp(detail.getExp());
                builder.addDetailList(detailBuilder.build());
            }
        }

        humanObj.sendMsg(builder.build());
    }

    /**
     * 更新钓鱼任务进度
     */
    private void updateFishTasks(HumanObject humanObj, int conditionType, long progress) {
        FishDataStructure dataStructure = humanObj.operation.fishData.getDataStructure();

//        // 更新每日任务
//        updateTaskProgress(dataStructure.getDailyTasks(), conditionType, progress);
//
//        // 更新渔场解锁任务
//        updateTaskProgress(dataStructure.getFishGroundTasks(), conditionType, progress);

        // 检查任务完成状态
        checkTaskCompletion(humanObj);
    }

    /**
     * 更新任务进度
     */
    private void updateTaskProgress(Map<Long, FishTask> taskMap, int conditionType, long progress) {
        for (FishTask task : taskMap.values()) {
            if (task.getType() == conditionType && task.getState() == 0) { // 进行中的任务
                task.setCount(task.getCount() + progress);
            }
        }
    }

    /**
     * 检查任务完成状态
     */
    private void checkTaskCompletion(HumanObject humanObj) {
//        FishDataStructure dataStructure = humanObj.operation.fishData.getDataStructure();
//        List<FishTask> completedTasks = new ArrayList<>();
//
//        // 检查每日任务
//        for (FishTask task : dataStructure.getDailyTasks().values()) {
//            if (task.getState() == 0 && isTaskCompleted(task)) {
//                task.setState(1); // 可领取
//                completedTasks.add(task);
//            }
//        }
//
//        // 检查渔场解锁任务
//        for (FishTask task : dataStructure.getFishGroundTasks().values()) {
//            if (task.getState() == 0 && isTaskCompleted(task)) {
//                task.setState(1); // 可领取
//                completedTasks.add(task);
//            }
//        }
//
//        // 如果有任务完成，发送任务更新消息
//        if (!completedTasks.isEmpty()) {
//            sendTaskUpdateMessage(humanObj, completedTasks);
//
//            // 保存任务数据
//            HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
//            homeFish.setDailyTasks(dataStructure.saveDailyTasksToJson());
//            homeFish.setFishGroundTasks(dataStructure.saveFishGroundTasksToJson());
//            homeFish.update();
//        }
    }

    /**
     * 判断任务是否完成
     */
    private boolean isTaskCompleted(FishTask task) {
        // TODO: 根据任务配置检查是否完成
        // 这里需要读取任务配置表来判断完成条件
        return task.getCount() >= 10; // 临时逻辑，实际需要读配置
    }

    /**
     * 发送任务更新消息
     */
    private void sendTaskUpdateMessage(HumanObject humanObj, List<FishTask> completedTasks) {
        MsgTask.task_update_s2c.Builder builder = MsgTask.task_update_s2c.newBuilder();

        for (FishTask task : completedTasks) {
            Define.p_task.Builder taskBuilder = Define.p_task.newBuilder();
            taskBuilder.setTaskId(task.getTaskId());
            taskBuilder.setState(task.getState());
            taskBuilder.setCount(task.getCount());
            taskBuilder.setType(task.getType());
            builder.addTaskList(taskBuilder.build());
        }

        humanObj.sendMsg(builder.build());

        Log.fish.debug("发送任务更新消息, humanId={}, completedCount={}",
            humanObj.id, completedTasks.size());
    }

    /**
     * 生成下一个渔场解锁任务
     */
    private void generateNextGroundTasks(HumanObject humanObj, int currentGroundSn) {
        // TODO: 根据配置生成下一个渔场的解锁任务
        Log.fish.debug("生成下一渔场解锁任务, humanId={}, currentGroundSn={}",
            humanObj.id, currentGroundSn);
    }

    /**
     * 升级单个鱼类
     */
    private void upgradeSingleFish(HumanObject humanObj, int fishTypeSn) {
        FishDataStructure dataStructure = humanObj.operation.fishData.getDataStructure();
        Map<Integer, FishDetail> albumMap = dataStructure.getAlbumDetailMap();

        if (!albumMap.containsKey(fishTypeSn)) {
            Log.fish.warn("鱼类不存在, humanId={}, fishTypeSn={}", humanObj.id, fishTypeSn);
            return;
        }

        FishDetail detail = albumMap.get(fishTypeSn);

        // 检查是否可以升级
        ConfFishLevel levelConfig = ConfFishLevel.get(fishTypeSn, detail.getLevel() + 1);
        if (levelConfig == null) {
            Log.fish.warn("鱼类升级配置不存在, humanId={}, fishTypeSn={}, currentLevel={}",
                humanObj.id, fishTypeSn, detail.getLevel());
            return;
        }

        // 检查经验是否足够
        if (detail.getExp() < levelConfig.need) {
            Log.fish.warn("鱼类经验不足, humanId={}, fishTypeSn={}, currentExp={}, needExp={}",
                humanObj.id, fishTypeSn, detail.getExp(), levelConfig.need);
            return;
        }

        // 检查货币是否足够
        if (levelConfig.cost != null && levelConfig.cost.length >= 2) {
            // TODO: 检查货币是否足够
        }

        // 执行升级
        detail.setLevel(detail.getLevel() + 1);
        detail.setExp(detail.getExp() - levelConfig.need);

        // TODO: 扣除货币

        // 更新图鉴
        albumMap.put(fishTypeSn, detail);
        // 保存数据结构到实体
        humanObj.operation.fishData.saveDataStructure();

        // 重新计算评分
        updateTotalScore(humanObj);

        Log.fish.debug("鱼类升级成功, humanId={}, fishTypeSn={}, newLevel={}",
            humanObj.id, fishTypeSn, detail.getLevel());
    }

    /**
     * 一键升级所有鱼
     */
    private void upgradeAllFish(HumanObject humanObj) {
        FishDataStructure dataStructure = humanObj.operation.fishData.getDataStructure();
        Map<Integer, FishDetail> albumMap = dataStructure.getAlbumDetailMap();

        boolean hasUpgrade = false;

        for (Map.Entry<Integer, FishDetail> entry : albumMap.entrySet()) {
            int fishTypeSn = entry.getKey();
            FishDetail detail = entry.getValue();

            // 尝试升级到最大等级
            while (true) {
                ConfFishLevel levelConfig = ConfFishLevel.get(fishTypeSn, detail.getLevel() + 1);
                if (levelConfig == null) break;

                if (detail.getExp() < levelConfig.need) break;

                // TODO: 检查货币是否足够

                // 执行升级
                detail.setLevel(detail.getLevel() + 1);
                detail.setExp(detail.getExp() - levelConfig.need);
                hasUpgrade = true;

                // TODO: 扣除货币
            }

            albumMap.put(entry.getKey(), detail);
        }

        if (hasUpgrade) {
            // 保存数据结构到实体
            humanObj.operation.fishData.saveDataStructure();
            updateTotalScore(humanObj);
            Log.fish.debug("一键升级完成, humanId={}", humanObj.id);
        }
    }

    /**
     * 更新总评分
     */
    private void updateTotalScore(HumanObject humanObj) {
        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        FishDataStructure dataStructure = humanObj.operation.fishData.getDataStructure();
        Map<Integer, FishDetail> albumMap = dataStructure.getAlbumDetailMap();

        long totalScore = 0;

        for (Map.Entry<Integer, FishDetail> entry : albumMap.entrySet()) {
            int fishTypeSn = entry.getKey();
            FishDetail detail = entry.getValue();

            // 检查是否是珍贵鱼
            ConfFishGroup groupConfig = ConfFishGroup.get(fishTypeSn);
            if (groupConfig != null && groupConfig.type != EFishType.FISH_TYPE_PRECIOUS) {
                // 非珍贵鱼才有评分
                ConfFishBase fishBase = ConfFishBase.get(detail.getFishSn());
                ConfFishLevel levelConfig = ConfFishLevel.get(fishTypeSn, detail.getLevel());

                if (fishBase != null && levelConfig != null) {
                    long fishScore = (long)fishBase.score * levelConfig.score_mult / 100;
                    totalScore += fishScore;
                }
            }
        }

        // 设置long类型的总分
        homeFish.setTotalScore(totalScore);
    }

    /**
     * 检查图鉴升级
     */
    private void checkAlbumLevelUp(HumanObject humanObj) {
        FishDataStructure dataStructure = humanObj.operation.fishData.getDataStructure();
        Map<Integer, FishDetail> albumMap = dataStructure.getAlbumDetailMap();

        // 累加所有鱼的等级
        int totalFishLevel = 0;
        for (Map.Entry<Integer, FishDetail> entry : albumMap.entrySet()) {
            FishDetail detail = entry.getValue();
            totalFishLevel += detail.getLevel();
        }

        while (true) {
            ConfFishBook fishBookConfig = ConfFishBook.get(humanObj.operation.fishData.getHomeFish().getAlbumLv() + 1);
            if (fishBookConfig == null) {
                break;
            }
            if (totalFishLevel >= fishBookConfig.need) {
                humanObj.operation.fishData.getHomeFish().setAlbumLv(humanObj.operation.fishData.getHomeFish().getAlbumLv() + 1);
                humanObj.operation.fishData.getHomeFish().setAlbumExp(totalFishLevel - fishBookConfig.need);
            } else {
                break;
            }
        }

        Log.fish.debug("检查图鉴升级, humanId={}, totalFishLevel={}", humanObj.id, totalFishLevel);
    }

    /**
     * 处理渔具升级请求
     */
    public void handleFishToolLvUpC2S(HumanObject humanObj, int toolType) {
        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            Log.fish.error("钓鱼数据未初始化, humanId={}", humanObj.id);
            return;
        }

        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        FishDataStructure dataStructure = humanObj.operation.fishData.getDataStructure();
        Map<Integer, Integer> toolMap = dataStructure.getFishTools();

        int currentLevel = toolMap.getOrDefault(toolType, 0);
        int nextLevel = currentLevel + 1;

        // 获取升级配置
        ConfFishTool toolConfig = ConfFishTool.get(toolType, nextLevel);
        if (toolConfig == null) {
            Log.fish.warn("渔具升级配置不存在, humanId={}, toolType={}, nextLevel={}",
                humanObj.id, toolType, nextLevel);
            return;
        }

        // 检查前置条件
        if (toolConfig.condition != null) {
            for (int[] condition : toolConfig.condition) {
                if (condition.length >= 2) {
                    int requiredToolType = condition[0];
                    int requiredLevel = condition[1];
                    int currentRequiredLevel = toolMap.getOrDefault(requiredToolType, 0);

                    if (currentRequiredLevel < requiredLevel) {
                        Log.fish.warn("渔具升级前置条件不满足, humanId={}, toolType={}, requiredToolType={}, requiredLevel={}, currentLevel={}",
                            humanObj.id, toolType, requiredToolType, requiredLevel, currentRequiredLevel);
                        return;
                    }
                }
            }
        }

        // 检查消耗
        if(!ProduceManager.inst().checkAndCostItem(humanObj, toolConfig.cost[0], toolConfig.cost[1], MoneyItemLogKey.钓鱼渔具升级).success){
            Log.fish.error("升级渔具失败道具不足, humanId={}, toolType={}", humanObj.id, toolType);
            return;
        }

        // 执行升级
        toolMap.put(toolType, nextLevel);
        // 保存数据结构到实体
        dataStructure.saveFishToolsToHomeFish(homeFish);

        // 响应客户端
        MsgHome.home_fish_tool_lv_up_s2c.Builder builder = MsgHome.home_fish_tool_lv_up_s2c.newBuilder();
        builder.setToolType(toolType);
        builder.setToolLv(nextLevel);
        humanObj.sendMsg(builder.build());

        Log.fish.debug("渔具升级成功, humanId={}, toolType={}, newLevel={}",
            humanObj.id, toolType, nextLevel);
    }

    /**
     * 处理解锁鱼塘槽位请求
     */
    public void handleFishUnlockHouseSlotC2S(HumanObject humanObj, int locationId) {
        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            Log.fish.error("钓鱼数据未初始化, humanId={}", humanObj.id);
            return;
        }

        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        FishDataStructure dataStructure = humanObj.operation.fishData.getDataStructure();
        Map<Integer, Integer> houseMap = dataStructure.getHouseList();

        // 检查槽位是否已解锁
        if (houseMap.containsKey(locationId)) {
            Log.fish.warn("槽位已解锁, humanId={}, locationId={}", humanObj.id, locationId);
            return;
        }

        // 获取解锁配置
        ConfFishHouse houseConfig = ConfFishHouse.get(locationId);
        if (houseConfig == null) {
            Log.fish.warn("鱼塘槽位配置不存在, humanId={}, locationId={}", humanObj.id, locationId);
            return;
        }

        // 检查解锁顺序 - 槽位要按顺序解锁
        if (!checkHouseSlotOrder(houseMap, locationId)) {
            Log.fish.warn("槽位解锁顺序错误, humanId={}, locationId={}", humanObj.id, locationId);
            return;
        }

        // 检查解锁消耗
        if (houseConfig.cost != null && houseConfig.cost.length >= 2) {
            if(!ProduceManager.inst().checkAndCostItem(humanObj, houseConfig.cost[0], houseConfig.cost[1], MoneyItemLogKey.钓鱼槽位解锁升级).success){
                Log.fish.error("解锁鱼塘槽位失败道具不足, humanId={}, locationId={}", humanObj.id, locationId);
                return;
            }
        }

        // 执行解锁
        houseMap.put(locationId, 0); // 0表示已解锁但未装备
        // 保存数据结构到实体
        homeFish.setHouseList(Utils.mapIntIntToJSON(houseMap));

        // 响应客户端
        MsgHome.home_fish_unlock_house_slot_s2c.Builder builder = MsgHome.home_fish_unlock_house_slot_s2c.newBuilder();
        builder.setLocationId(locationId);
        humanObj.sendMsg(builder.build());

        Log.fish.debug("解锁鱼塘槽位成功, humanId={}, locationId={}", humanObj.id, locationId);
    }

    /**
     * 检查鱼塘槽位解锁顺序
     */
    private boolean checkHouseSlotOrder(Map<Integer, Integer> houseMap, int locationId) {
        // 检查前面的槽位是否都已解锁
        for (int i = 1; i < locationId; i++) {
            if (!houseMap.containsKey(i)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 处理鱼塘装备鱼请求
     */
    public void handleFishHouseEquipC2S(HumanObject humanObj, int locationId, int fishGroupSn) {
        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            Log.fish.error("钓鱼数据未初始化, humanId={}", humanObj.id);
            return;
        }

         HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        FishDataStructure dataStructure = humanObj.operation.fishData.getDataStructure();
        Map<Integer, Integer> houseMap = dataStructure.getHouseList();

        // 检查槽位是否已解锁
        if (!houseMap.containsKey(locationId)) {
            Log.fish.warn("槽位未解锁, humanId={}, locationId={}", humanObj.id, locationId);
            return;
        }

        if (fishGroupSn > 0) {
            // 装备鱼类
            if (!validateFishEquip(humanObj, locationId, fishGroupSn, houseMap)) {
                return;
            }
        }

        // 更新装备
        houseMap.put(locationId, fishGroupSn); // 0表示卸下，>0表示装备的鱼类组SN
        // 保存数据结构到实体
        homeFish.setHouseList(Utils.mapIntIntToJSON(houseMap));

        //重新计算所有已装备鱼提供的"展示属性"总和，并更新玩家的战斗属性
        updateEquipPorpCalcPower(humanObj);

        // 响应客户端
        MsgHome.home_fish_house_equip_s2c.Builder builder = MsgHome.home_fish_house_equip_s2c.newBuilder();
        builder.setLocationId(locationId);
        builder.setFishTypeSn(fishGroupSn);
        humanObj.sendMsg(builder.build());

        Log.fish.debug("鱼塘装备更新, humanId={}, locationId={}, fishGroupSn={}",
            humanObj.id, locationId, fishGroupSn);
    }

    /**
     * 验证鱼类装备
     */
    private boolean validateFishEquip(HumanObject humanObj, int locationId, int fishGroupSn,
                                    Map<Integer, Integer> houseMap) {

        // 检查槽位是否允许放入该类型的鱼
        ConfFishHouse houseConfig = ConfFishHouse.get(locationId);
        if (houseConfig == null) {
            Log.fish.warn("鱼塘槽位配置不存在, humanId={}, locationId={}", humanObj.id, locationId);
            return false;
        }

        ConfFishGroup groupConfig = ConfFishGroup.get(fishGroupSn);
        if (groupConfig == null) {
            Log.fish.warn("鱼类组配置不存在, humanId={}, fishGroupSn={}", humanObj.id, fishGroupSn);
            return false;
        }

        if (houseConfig.fish_type != 0 && houseConfig.fish_type != groupConfig.type) {
            Log.fish.warn("槽位不允许放入该类型的鱼, humanId={}, locationId={}, fishType={}, allowedType={}",
                humanObj.id, locationId, groupConfig.type, houseConfig.fish_type);
            return false;
        }

        // 检查该鱼是否已在其他槽位装备
        for (Map.Entry<Integer, Integer> entry : houseMap.entrySet()) {
            if (!entry.getKey().equals(locationId)) {
                int equippedFishSn = entry.getValue();
                if (equippedFishSn == fishGroupSn) {
                    Log.fish.warn("该鱼已在其他槽位装备, humanId={}, fishGroupSn={}, otherLocationId={}",
                        humanObj.id, fishGroupSn, entry.getKey());
                    return false;
                }
            }
        }

        // 检查玩家是否拥有该鱼类
        FishDataStructure dataStructure = humanObj.operation.fishData.getDataStructure();
        Map<Integer, FishDetail> albumMap = dataStructure.getAlbumDetailMap();
        if (!albumMap.containsKey(fishGroupSn)) {
            Log.fish.warn("玩家未拥有该鱼类, humanId={}, fishGroupSn={}", humanObj.id, fishGroupSn);
            return false;
        }

        return true;
    }

    /**
     * 鱼塘装扮解锁升级
     */
    public void handleFishHouseDesignLevelUpC2S(HumanObject humanObj, int sn) {
        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            Log.fish.error("钓鱼数据未初始化, humanId={}", humanObj.id);
            return;
        }
        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        FishDataStructure dataStructure = humanObj.operation.fishData.getDataStructure();
        Map<Integer, Integer> designMap = dataStructure.getHouseDesign();
        int level = designMap.getOrDefault(sn, 0);
        ConfFishDesign_0 designConfig = ConfFishDesign_0.get(sn, level);
        if(designConfig == null) {
            Log.fish.error("装扮激活升级找不到配置，sn={}, lv={}",sn, level);
            return;
        }
        if(!ProduceManager.inst().checkAndCostItem(humanObj, designConfig.expend[0], designConfig.expend[1], MoneyItemLogKey.钓鱼养成).success){
            Log.fish.error("装扮激活升级道具不足，humanId={}",humanObj.id);
            return;
        }
        designMap.put(sn, level+1);
        homeFish.setHouseDesign(Utils.mapIntIntToJSON(designMap));
        updatePorpCalcPower(humanObj);

        MsgHome.home_fish_house_design_level_up_s2c.Builder builder = MsgHome.home_fish_house_design_level_up_s2c.newBuilder();
        builder.setSn(sn);
        builder.setLevel(level+1);
        humanObj.sendMsg(builder.build());
    }

    /**
     * 鱼塘装扮使用
     */
    public void handleFishHouseSetUseDesignC2S(HumanObject humanObj, int sn) {
        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            Log.fish.error("钓鱼数据未初始化, humanId={}", humanObj.id);
            return;
        }
        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        FishDataStructure dataStructure = humanObj.operation.fishData.getDataStructure();
        Map<Integer, Integer> designMap = dataStructure.getHouseDesign();
        if(!designMap.containsKey(sn)){
            Log.fish.error("装扮未解锁，humanId={}, sn={}",humanObj.id, sn);
            return;
        }
        homeFish.setUseHouseDesign(sn);
    }

    private void updatePorpCalcPower(HumanObject humanObj){
        FishDataStructure dataStructure = humanObj.operation.fishData.getDataStructure();
        PropCalc propCalc = new PropCalc();
        int power = 0;
        Map<Integer, Integer> designMap = dataStructure.getHouseDesign();
        for (Map.Entry<Integer, Integer> entry : designMap.entrySet()) {
            ConfFishDesign_0 designConfig = ConfFishDesign_0.get(entry.getKey(), entry.getValue());
            if(designConfig == null) continue;
            propCalc.plus(designConfig.own_attrs);
            power += designConfig.power;
        }
        humanObj.dataPers.unitPropPlus.setFieldRead(UnitPropPlus.K.fish, propCalc.toJSONStr());
        String oldPower = humanObj.getHuman().getCombat();
        HumanManager.inst().updatePowerPar(humanObj, EModule.fish, power);
        PropManager.inst().propCalc(humanObj, CombatChangeLog.钓鱼);
        if(oldPower.equals(humanObj.getHuman().getCombat())){
            HumanManager.inst().sendMsg_role_total_sp_update_s2c(humanObj);
        }
    }

    private void updateEquipPorpCalcPower(HumanObject humanObj){
        FishDataStructure dataStructure = humanObj.operation.fishData.getDataStructure();
        PropCalc propCalc = new PropCalc();
        Map<Integer, Integer> houseList = dataStructure.getHouseList();
        Map<Integer, FishDetail> albumDetailMap = dataStructure.getAlbumDetailMap();
        for (Map.Entry<Integer, Integer> entry : houseList.entrySet()) {
            int fishGroupSn = entry.getValue();
            if(fishGroupSn == 0) continue;
            int fishSn = albumDetailMap.get(fishGroupSn).getFishSn();
            ConfFishBase fishBase = ConfFishBase.get(fishSn);
            if(fishBase == null){
                Log.fish.error("鱼塘装备鱼类基础配置不存在，humanId={}, fishSn={}",humanObj.id, fishSn);
                continue;
            }
            propCalc.plus(fishBase.attr_show);
        }
        humanObj.dataPers.unitPropPlus.setFieldRead(UnitPropPlus.K.fish, propCalc.toJSONStr());
        PropManager.inst().propCalc(humanObj, CombatChangeLog.钓鱼);
        String oldPower = humanObj.getHuman().getCombat();
        if(oldPower.equals(humanObj.getHuman().getCombat())){
            HumanManager.inst().sendMsg_role_total_sp_update_s2c(humanObj);
        }
    }

    /**
     * 处理开始自动钓鱼请求
     */
    public void handleFishStartAutoC2S(HumanObject humanObj, int groundSn,
                                       List<Define.p_key_value> baitSnNumList, int castNum) {
        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            Log.fish.error("钓鱼数据未初始化, humanId={}", humanObj.id);
            return;
        }

        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();

        // 检查是否已在自动钓鱼
        if (homeFish.getAutoFishGroundSn() > 0) {
            Log.fish.warn("已在自动钓鱼中, humanId={}", humanObj.id);
            return;
        }

        // 验证参数
        if (!validateAutoFishParams(humanObj, groundSn, baitSnNumList, castNum)) {
            return;
        }
        List<List<Integer>> baitList = parseBaitSnNumList(baitSnNumList);
        // 设置自动钓鱼状态
        homeFish.setAutoFishGroundSn(groundSn);
        homeFish.setAutoFishStartTime(Port.getTime());
        homeFish.setAutoFishBaitsSnNum(Utils.toJSONString(baitList));
        homeFish.setAutoFishCastNum(castNum);
        homeFish.setAutoFishSettleCount(0);

        // 清空统计数据
        homeFish.setTotalAlbumFishes("{}");
        homeFish.setTotalSellFishes("{}");
        homeFish.setTotalCastCnt(0);
        homeFish.setTotalReelSuccCnt(0);
        homeFish.setTotalSlippedCnt(0);
        homeFish.setTotalRewards("{}");

        // 响应客户端
        MsgHome.home_fish_start_auto_s2c.Builder builder = MsgHome.home_fish_start_auto_s2c.newBuilder();
        builder.setStartTime((int)(homeFish.getAutoFishStartTime() / Time.SEC) + 1);
        builder.setFishGround(groundSn);

        // 添加鱼饵信息到响应
        builder.addAllBaitSnNum(baitSnNumList);
        builder.setMultiple(castNum);

        humanObj.sendMsg(builder.build());

        Log.fish.debug("开始自动钓鱼, humanId={}, groundSn={}, castNum={}",
            humanObj.id, groundSn, castNum);
    }

    /**
     * 验证自动钓鱼参数
     */
    private boolean validateAutoFishParams(HumanObject humanObj, int groundSn,
                                           List<Define.p_key_value> baitSnNumList, int castNum) {

        // 检查抛竿次数
        if (castNum != 1 && castNum != 10) {
            Log.fish.warn("无效的自动钓鱼抛竿次数, humanId={}, castNum={}", humanObj.id, castNum);
            return false;
        }

        // 检查渔场配置
        ConfFishGround groundConfig = ConfFishGround.get(groundSn);
        if (groundConfig == null) {
            Log.fish.warn("渔场配置不存在, humanId={}, groundSn={}", humanObj.id, groundSn);
            return false;
        }

        // 检查鱼饵列表
        if (baitSnNumList == null || baitSnNumList.isEmpty()) {
            Log.fish.warn("鱼饵列表为空, humanId={}", humanObj.id);
            return false;
        }

        int totalBaitNum = 0;
        for (Define.p_key_value baitInfo : baitSnNumList) {
            int baitSn = (int)baitInfo.getK();
            int baitNum = (int)baitInfo.getV();
            totalBaitNum += baitNum;
            ConfFishBait baitConfig = ConfFishBait.get(baitSn);
            if (baitConfig == null) {
                Log.fish.warn("鱼饵配置不存在, humanId={}, baitSn={}", humanObj.id, baitSn);
                return false;
            }
            int itemSn = baitConfig.item_id;
            int realBaitNum = ItemManager.inst().getItemNum(humanObj, itemSn);
            if (realBaitNum < baitNum) {
                Log.fish.warn("鱼饵数量不足, humanId={}, baitSn={}, baitNum={}, realBaitNum={}",
                    humanObj.id, baitSn, baitNum, realBaitNum);
                return false;
            }
        }
        if (castNum == 10 && totalBaitNum < 10) {
            Log.fish.warn("鱼饵数量不足, humanId={}, totalBaitNum={}", humanObj.id, totalBaitNum);
            return false;
        }
        return true;
    }

    public List<List<Integer>> parseBaitSnNumList(List<Define.p_key_value> baitSnNumList) {
        List<List<Integer>> result = new ArrayList<>();
        for (Define.p_key_value baitInfo : baitSnNumList) {
            int baitSn = (int)baitInfo.getK();
            int baitNum = (int)baitInfo.getV();
            result.add(Arrays.asList(baitSn, baitNum));
        }
        return result;
    }

    /**
     * 处理结束自动钓鱼请求
     */
    public void handleFishFinAutoC2S(HumanObject humanObj) {
        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            Log.fish.error("钓鱼数据未初始化, humanId={}", humanObj.id);
            return;
        }

        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        FishDataStructure dataStructure = humanObj.operation.fishData.getDataStructure();

        // 检查是否在自动钓鱼
        if (homeFish.getAutoFishGroundSn() <= 0) {
            Log.fish.warn("未在自动钓鱼中, humanId={}", humanObj.id);
            return;
        }

        // 保存图鉴数据（自动钓鱼过程中可能有图鉴更新）
        homeFish.setAlbumDetailMap(dataStructure.saveAlbumDetailMapToJson());

        // 响应客户端，返回累积的统计数据
        sendAutoFishFinResponse(humanObj);

        // 清空自动钓鱼状态
        homeFish.setAutoFishGroundSn(0);
        homeFish.setAutoFishStartTime(0);
        homeFish.setAutoFishBaitsSnNum("[]");
        homeFish.setAutoFishCastNum(0);
        homeFish.setAutoFishSettleCount(0);

        // 清空累积统计数据
        homeFish.setTotalAlbumFishes("{}");
        homeFish.setTotalSellFishes("{}");
        homeFish.setTotalCastCnt(0);
        homeFish.setTotalReelSuccCnt(0);
        homeFish.setTotalSlippedCnt(0);
        homeFish.setTotalRewards("{}");

        homeFish.update();

        Log.fish.debug("结束自动钓鱼, humanId={}", humanObj.id);
    }

    /**
     * 模拟自动钓鱼消耗
     */
    private int simulateAutoFishing(HumanObject humanObj, int totalFishingTimes) {
        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();

        // 获取鱼饵配置
        String baitSnNumJson = homeFish.getAutoFishBaitsSnNum();
        List<List<Integer>> baitSnNumList = JSON.parseObject(
                baitSnNumJson,
                new TypeReference<List<List<Integer>>>() {}
        );

        // TODO: 根据鱼饵数量计算实际可进行的钓鱼次数
        // 这里需要道具系统支持

        return Math.min(totalFishingTimes, 1000); // 临时限制最大次数
    }

    /**
     * 发送自动钓鱼结束响应
     */
    private void sendAutoFishFinResponse(HumanObject humanObj) {
        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();

        MsgHome.home_fish_fin_auto_s2c.Builder builder = MsgHome.home_fish_fin_auto_s2c.newBuilder();

        // 更新图鉴的鱼
        Map<String, Object> totalAlbumMap = Utils.jsonToMap(homeFish.getTotalAlbumFishes());
        for (Map.Entry<String, Object> entry : totalAlbumMap.entrySet()) {
            int fishGroupSn = Utils.intValue(entry.getKey());
            JSONObject detailJson = (JSONObject) entry.getValue();
            FishDetail detail = detailJson.toJavaObject(FishDetail.class);

            Define.p_home_fish_detail.Builder detailBuilder = Define.p_home_fish_detail.newBuilder();
            detailBuilder.setFishTypeSn(fishGroupSn);
            detailBuilder.setFishSn(detail.getFishSn());
            detailBuilder.setLen(detail.getMaxLen());
            detailBuilder.setLv(detail.getLevel());
            detailBuilder.setExp(detail.getExp());
            builder.addAlbumFishes(detailBuilder.build());
        }

        // 卖出的鱼
        Map<String, Object> totalSellMap = Utils.jsonToMap(homeFish.getTotalSellFishes());
        for (Map.Entry<String, Object> entry : totalSellMap.entrySet()) {
            int fishGroupSn = Utils.intValue(entry.getKey());
            int count = Utils.intValue(entry.getValue());

            Define.p_key_value.Builder kvBuilder = Define.p_key_value.newBuilder();
            kvBuilder.setK(fishGroupSn);
            kvBuilder.setV(count);
            builder.addSellFishes(kvBuilder.build());
        }

        // 统计数据
        builder.setCastCnt(homeFish.getTotalCastCnt());
        builder.setReelSuccCnt(homeFish.getTotalReelSuccCnt());
        builder.setSlippedCnt(homeFish.getTotalSlippedCnt());

        // 出售获得的奖励
        Map<String, Object> totalRewardMap = Utils.jsonToMap(homeFish.getTotalRewards());
        for (Map.Entry<String, Object> entry : totalRewardMap.entrySet()) {
            int currencyId = Utils.intValue(entry.getKey());
            long amount = Utils.longValue(entry.getValue());

            Define.p_reward.Builder rewardBuilder = Define.p_reward.newBuilder();
            rewardBuilder.setGtid(currencyId);
            rewardBuilder.setNum(amount);
            builder.addReward(rewardBuilder.build());
        }

        humanObj.sendMsg(builder.build());
    }

    /**
     * 处理自动钓鱼结算请求
     */
    public void handleFishAutoSettleC2S(HumanObject humanObj) {
        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            Log.fish.error("钓鱼数据未初始化, humanId={}", humanObj.id);
            return;
        }

        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();

        // 检查是否在自动钓鱼
        if (homeFish.getAutoFishGroundSn() <= 0) {
            Log.fish.warn("未在自动钓鱼中, humanId={}", humanObj.id);
            return;
        }

        // 获取自动钓鱼间隔配置
        ConfFishConfig intervalConfig = ConfFishConfig.get(EFishType.CONF_AUTO_FISH_INTERVAL);
        int intervalSeconds = 10; // 默认10秒一轮
        if (intervalConfig != null && intervalConfig.parameter != null && intervalConfig.parameter.length > 0) {
            intervalSeconds = intervalConfig.parameter[0][0];
        }

        // 计算当前可结算的轮次
        long startTime = homeFish.getAutoFishStartTime();
        long currentTime = Port.getTime();
        long elapsedSeconds = (currentTime - startTime) / Time.SEC;

        int totalRounds = (int)(elapsedSeconds / intervalSeconds); // 总轮次
        int alreadySettledRounds = homeFish.getAutoFishSettleCount(); // 已结算轮次
        int newRounds = totalRounds - alreadySettledRounds; // 新的可结算轮次

        if (newRounds <= 0) {
            // 没有新的可结算轮次，返回下次结算时间
            int nextSettleTime = getNextSettleTime(homeFish, intervalSeconds);
            sendAutoSettleResponse(humanObj, 0, getCurrentBaitSn(homeFish), nextSettleTime, 0, 0);
            return;
        }

        // 执行多轮钓鱼结算（结算所有可结算的轮次）
        AutoFishResult totalResult = executeMultipleRoundsAutoFish(humanObj, newRounds);

        // 更新已结算轮次
        homeFish.setAutoFishSettleCount(alreadySettledRounds + newRounds);

        // 累积到总统计中
        accumulateAutoFishStats(homeFish, totalResult);

        // 计算下次结算时间
        int nextSettleTime = getNextSettleTime(homeFish, intervalSeconds);

        //鱼饵消耗完了就结束自动钓鱼
        if (totalResult.baitSn == 0) {
            handleFishFinAutoC2S(humanObj);
        }else{
            // 响应客户端（返回总的钓鱼结果，鱼信息是最后一轮的第一条鱼）
            sendAutoSettleResponse(humanObj, totalResult.fishCount, totalResult.baitSn, nextSettleTime,
                    totalResult.firstFishSn, totalResult.firstFishLen);
        }

        Log.fish.debug("自动钓鱼结算完成, humanId={}, rounds={}, totalFishCount={}, firstFish={}",
            humanObj.id, newRounds, totalResult.fishCount, totalResult.firstFishSn);
    }

    /**
     * 执行多轮自动钓鱼
     */
    private AutoFishResult executeMultipleRoundsAutoFish(HumanObject humanObj, int rounds) {
        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        int groundSn = homeFish.getAutoFishGroundSn();
        int multiple = homeFish.getAutoFishCastNum(); // 1钓或10钓

        // 总结果
        AutoFishResult totalResult = new AutoFishResult();
        totalResult.fishCount = 0;
        totalResult.successCount = 0;
        totalResult.slippedCount = 0;

        // 执行多轮钓鱼
        for (int round = 0; round < rounds; round++) {
            // 获取当前轮次使用的鱼饵
            int currentBaitSn = getCurrentBaitSnForRound(homeFish, round);
            if (currentBaitSn <= 0) {
                Log.fish.warn("鱼饵不足，自动钓鱼中断, humanId={}, round={}", humanObj.id, round);
                break;
            }

            // 执行一轮钓鱼
            AutoFishResult roundResult = executeOneRoundAutoFish(humanObj, currentBaitSn, multiple);

            // 累积结果
            totalResult.fishCount += roundResult.fishCount;
            totalResult.successCount += roundResult.successCount;
            totalResult.slippedCount += roundResult.slippedCount;
            totalResult.baitSn = currentBaitSn; // 记录最后使用的鱼饵

            // 记录最后一轮的第一条鱼信息（用于展示）
            if (roundResult.firstFishSn > 0) {
                totalResult.firstFishSn = roundResult.firstFishSn;
                totalResult.firstFishLen = roundResult.firstFishLen;
            }

            // 合并图鉴更新
            for (Map.Entry<Integer, FishDetail> entry : roundResult.albumFishes.entrySet()) {
                totalResult.albumFishes.put(entry.getKey(), entry.getValue());
            }

            // 合并卖出统计
            for (Map.Entry<Integer, Integer> entry : roundResult.sellFishes.entrySet()) {
                totalResult.sellFishes.put(entry.getKey(),
                    totalResult.sellFishes.getOrDefault(entry.getKey(), 0) + entry.getValue());
            }

            // 合并奖励
            for (Map.Entry<Integer, Long> entry : roundResult.rewards.entrySet()) {
                totalResult.rewards.put(entry.getKey(),
                    totalResult.rewards.getOrDefault(entry.getKey(), 0L) + entry.getValue());
            }

            // 消耗鱼饵
            consumeBaitForRound(homeFish, currentBaitSn, multiple);
        }

        return totalResult;
    }

    /**
     * 执行一轮自动钓鱼
     */
    private AutoFishResult executeOneRoundAutoFish(HumanObject humanObj, int baitSn, int multiple) {
        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        FishDataStructure dataStructure = humanObj.operation.fishData.getDataStructure();

        int groundSn = homeFish.getAutoFishGroundSn();

        AutoFishResult result = new AutoFishResult(multiple, baitSn);

        // 执行钓鱼
        for (int i = 0; i < multiple; i++) {
            // 生成鱼类
            int fishSn = executeFishing(humanObj, groundSn, baitSn);
            if (fishSn <= 0) continue;

            ConfFishBase fishBase = ConfFishBase.get(fishSn);
            if (fishBase == null) continue;

            // 记录第一条鱼
            if (result.firstFishSn == 0) {
                result.firstFishSn = fishSn;
                result.firstFishLen = Utils.random(fishBase.len_range[0], fishBase.len_range[1]);
            }

            // 逃跑判定
            if (calculateEscapeRate(humanObj, fishBase)) {
                result.slippedCount++;
                continue;
            }

            result.successCount++;

            // 处理奖励
            processAutoFishRewards(fishBase, result);

            // 更新图鉴
            updateAutoFishAlbum(humanObj, fishBase, result);

            // 统计卖出
            int fishGroupSn = fishBase.fish_group_id;
            result.sellFishes.put(fishGroupSn, result.sellFishes.getOrDefault(fishGroupSn, 0) + 1);
        }

        return result;
    }

    /**
     * 获取当前使用的鱼饵SN（兼容旧方法）
     */
    private int getCurrentBaitSn(HomeFish homeFish) {
        return getCurrentBaitSnForRound(homeFish, 0);
    }

    /**
     * 获取指定轮次使用的鱼饵SN
     */
    private int getCurrentBaitSnForRound(HomeFish homeFish, int roundOffset) {
        List<List<Integer>> baitSnNumList = JSON.parseObject(
                homeFish.getAutoFishBaitsSnNum(),
                new TypeReference<List<List<Integer>>>() {}
        );
        if (baitSnNumList == null || baitSnNumList.isEmpty()) {
            return 1; // 默认鱼饵
        }

        int multiple = homeFish.getAutoFishCastNum(); // 1钓或10钓
        int alreadySettledRounds = homeFish.getAutoFishSettleCount();
        int currentRound = alreadySettledRounds + roundOffset;

        // 计算已消耗的鱼饵总数
        int totalConsumed = currentRound * multiple;

        // 按顺序查找可用的鱼饵
        int consumed = 0;
        for (List<Integer> baitInfo : baitSnNumList) {
            if (baitInfo.size() < 2) continue;

            int baitSn = baitInfo.get(0);
            int baitCount = baitInfo.get(1);

            if (consumed + baitCount > totalConsumed) {
                // 这个鱼饵还有剩余，可以使用
                int remaining = consumed + baitCount - totalConsumed;
                if (remaining >= multiple) {
                    return baitSn; // 剩余数量足够本轮使用
                }
            }
            consumed += baitCount;
        }

        return 0; // 鱼饵不足
    }

    /**
     * 消耗指定轮次的鱼饵
     */
    private void consumeBaitForRound(HomeFish homeFish, int baitSn, int consumeCount) {
        List<List<Integer>> baitSnNumList = JSON.parseObject(
                homeFish.getAutoFishBaitsSnNum(),
                new TypeReference<List<List<Integer>>>() {}
        );
        if (baitSnNumList == null || baitSnNumList.isEmpty()) {
            return;
        }

        // 查找并消耗对应的鱼饵
        for (List<Integer> baitInfo : baitSnNumList) {
            if (baitInfo.size() >= 2 && baitInfo.get(0).equals(baitSn)) {
                int currentCount = baitInfo.get(1);
                int newCount = Math.max(0, currentCount - consumeCount);
                baitInfo.set(1, newCount);
                break;
            }
        }

        // 更新鱼饵列表
        homeFish.setAutoFishBaitsSnNum(JSON.toJSONString(baitSnNumList));

        Log.fish.debug("消耗鱼饵, humanId={}, baitSn={}, consumeCount={}",
            homeFish.getId(), baitSn, consumeCount);
    }

    /**
     * 处理自动钓鱼奖励
     */
    private void processAutoFishRewards(ConfFishBase fishBase, AutoFishResult result) {
        ConfFishGroup groupConfig = ConfFishGroup.get(fishBase.fish_group_id);
        if (groupConfig == null) return;

        // 发放货币奖励
        if (groupConfig.sell != null && groupConfig.sell.length >= 2) {
            int currencyId = groupConfig.sell[0];
            long amount = groupConfig.sell[1];
            result.rewards.put(currencyId, result.rewards.getOrDefault(currencyId, 0L) + amount);
        }
    }

    /**
     * 更新自动钓鱼图鉴
     */
    private void updateAutoFishAlbum(HumanObject humanObj, ConfFishBase fishBase, AutoFishResult result) {
        FishDataStructure dataStructure = humanObj.operation.fishData.getDataStructure();
        Map<Integer, FishDetail> albumMap = dataStructure.getAlbumDetailMap();

        int fishGroupId = fishBase.fish_group_id;
        int randomLen = Utils.random(fishBase.len_range[0], fishBase.len_range[1]);

        FishDetail currentDetail = albumMap.get(fishGroupId);
        boolean isNewRecord = false;

        if (currentDetail == null) {
            // 第一次钓到这种鱼
            currentDetail = new FishDetail(fishBase.sn, randomLen, 1, 1);
            isNewRecord = true;
        } else {
            // 检查是否破纪录
            if (randomLen > currentDetail.getMaxLen()) {
                currentDetail.setMaxLen(randomLen);
                currentDetail.setFishSn(fishBase.sn);
                isNewRecord = true;
            }
            // 累积经验
            currentDetail.setExp(currentDetail.getExp() + 1);
        }

        // 更新图鉴
        albumMap.put(fishGroupId, currentDetail);

        // 如果有更新，记录到结果中
        if (isNewRecord || !result.albumFishes.containsKey(fishGroupId)) {
            result.albumFishes.put(fishGroupId, currentDetail);
        }
    }

    /**
     * 累积自动钓鱼统计数据
     */
    private void accumulateAutoFishStats(HomeFish homeFish, AutoFishResult result) {
        // 累积基础统计
        homeFish.setTotalCastCnt(homeFish.getTotalCastCnt() + result.fishCount);
        homeFish.setTotalReelSuccCnt(homeFish.getTotalReelSuccCnt() + result.successCount);
        homeFish.setTotalSlippedCnt(homeFish.getTotalSlippedCnt() + result.slippedCount);

        // 累积图鉴鱼类
        Map<String, Object> totalAlbumMap = Utils.jsonToMap(homeFish.getTotalAlbumFishes());
        for (Map.Entry<Integer, FishDetail> entry : result.albumFishes.entrySet()) {
            totalAlbumMap.put(String.valueOf(entry.getKey()), entry.getValue());
        }
        homeFish.setTotalAlbumFishes(Utils.mapToJSON(totalAlbumMap));

        // 累积卖出鱼类
        Map<String, Object> totalSellMap = Utils.jsonToMap(homeFish.getTotalSellFishes());
        for (Map.Entry<Integer, Integer> entry : result.sellFishes.entrySet()) {
            String fishKey = String.valueOf(entry.getKey());
            int currentCount = Utils.intValue(totalSellMap.get(fishKey));
            totalSellMap.put(fishKey, currentCount + entry.getValue());
        }
        homeFish.setTotalSellFishes(Utils.mapToJSON(totalSellMap));

        // 累积奖励
        Map<String, Object> totalRewardMap = Utils.jsonToMap(homeFish.getTotalRewards());
        for (Map.Entry<Integer, Long> entry : result.rewards.entrySet()) {
            String currencyKey = String.valueOf(entry.getKey());
            long currentAmount = Utils.longValue(totalRewardMap.get(currencyKey));
            totalRewardMap.put(currencyKey, currentAmount + entry.getValue());
        }
        homeFish.setTotalRewards(Utils.mapToJSON(totalRewardMap));
    }

    /**
     * 获取下次结算时间
     */
    private int getNextSettleTime(HomeFish homeFish, int intervalSeconds) {
        long startTime = homeFish.getAutoFishStartTime();
        int settledRounds = homeFish.getAutoFishSettleCount();

        // 下一轮的结算时间
        long nextSettleTime = startTime + ((settledRounds + 1) * intervalSeconds * Time.SEC);

        return (int)(nextSettleTime / Time.SEC);
    }

    /**
     * 批量结算自动钓鱼 - 返回本次会话奖励
     */
    private Map<Integer, Integer> batchSettleAutoFishing(HumanObject humanObj, int fishingTimes) {
        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        int groundSn = homeFish.getAutoFishGroundSn();

        // 获取第一个鱼饵SN
        String baitSnNumJson = homeFish.getAutoFishBaitsSnNum();
        List<List<Integer>> baitSnNumList = JSON.parseObject(baitSnNumJson,new TypeReference<List<List<Integer>>>() {});
        int baitSn = 1; // 默认鱼饵
        if (!baitSnNumList.isEmpty() && !baitSnNumList.get(0).isEmpty()) {
            baitSn = baitSnNumList.get(0).get(0);
        }

        // 累积变量
        int sessionReelSucc = 0;
        int sessionSlipped = 0;
        Map<Integer, Integer> sessionRewards = new HashMap<>();
        FishDataStructure dataStructure = humanObj.operation.fishData.getDataStructure();

        // 循环模拟每次钓鱼
        for (int i = 0; i < fishingTimes; i++) {
            // 复用核心钓鱼逻辑
            int fishSn = executeFishing(humanObj, groundSn, baitSn);
            if (fishSn <= 0) continue;

            ConfFishBase fishBase = ConfFishBase.get(fishSn);
            if (fishBase == null) continue;

            // 逃跑判定
            if (calculateEscapeRate(humanObj, fishBase)) {
                sessionSlipped++;
                continue;
            }

            sessionReelSucc++;
            int randomLen = Utils.random(fishBase.len_range[0], fishBase.len_range[1]);

            // 累积奖励
            processRewards(humanObj, fishBase, sessionRewards);

            // 累积图鉴更新
            updateAlbumData(humanObj, fishBase, randomLen);
        }

        // 应用所有变更
        homeFish.setTotalCastCnt(homeFish.getTotalCastCnt() + fishingTimes);
        homeFish.setTotalReelSuccCnt(homeFish.getTotalReelSuccCnt() + sessionReelSucc);
        homeFish.setTotalSlippedCnt(homeFish.getTotalSlippedCnt() + sessionSlipped);

        updateTotalRewards(homeFish, sessionRewards);

        Log.fish.debug("自动钓鱼批量结算完成, humanId={}, times={}, succ={}, slipped={}",
            humanObj.id, fishingTimes, sessionReelSucc, sessionSlipped);

        return sessionRewards;
    }

    /**
     * 发送自动钓鱼结算响应
     */
    private void sendAutoSettleResponse(HumanObject humanObj, int fishCount, int baitSn,
                                      int settleTime, int firstFishSn, int firstFishLen) {

        MsgHome.home_fish_auto_settle_s2c.Builder builder = MsgHome.home_fish_auto_settle_s2c.newBuilder();

        builder.setFishCount(fishCount);
        builder.setBaitSn(baitSn);
        builder.setSettleTime(settleTime);
        builder.setFirstFishSn(firstFishSn);
        builder.setLen(firstFishLen);

        humanObj.sendMsg(builder.build());
    }

    public void init(HumanObject humanObj, HomeFish homeFish) {
        if (homeFish == null) {
            return;
        }
        humanObj.operation.fishData = new FishData();
        humanObj.operation.fishData.setHomeFish(homeFish);

    }

    public void getFish(HumanObject humanObj, int fishSn, int num) {
        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        if (humanObj.operation.fishData == null || homeFish == null) {
            return;
        }
        ConfFishBase fishBase = ConfFishBase.get(fishSn);
        if (fishBase == null) {
            return;
        }
        ConfFishGroup fishGroup = ConfFishGroup.get(fishBase.fish_group_id);
        if (fishGroup == null) {
            return;
        }
        FishDataStructure dataStructure = humanObj.operation.fishData.getDataStructure();
        Map<Integer, FishDetail> albumDetailMap = dataStructure.getAlbumDetailMap();
        FishDetail fishDetail = albumDetailMap.get(fishGroup.sn);
        if (fishDetail == null) {
            int randomLen = Utils.random(fishBase.len_range[0], fishBase.len_range[1]);
            fishDetail = new FishDetail(fishBase.sn, randomLen, 0, 0);
            albumDetailMap.put(fishGroup.sn, fishDetail);
        }
        int len = Utils.random(fishBase.len_range[0], fishBase.len_range[1]);
        if (len > fishDetail.getMaxLen()) {
            fishDetail.setMaxLen(len);
            fishDetail.setFishSn(fishBase.sn);
        }
        fishDetail.setExp(fishDetail.getExp() + num);
        homeFish.setAlbumDetailMap(dataStructure.saveAlbumDetailMapToJson());
        handleFishDataC2S(humanObj);
    }
}
