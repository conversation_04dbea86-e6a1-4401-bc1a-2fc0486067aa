package org.gof.demo.worldsrv.home.Fish;

import org.gof.demo.worldsrv.entity.HomeFish;
import java.util.List;
import java.util.ArrayList;

/**
 * 钓鱼数据 - 会话缓存(非持久化)
 */
public class FishData {
    private HomeFish homeFish;

    /** 数据结构缓存 */
    private FishDataStructure dataStructure;

    // ===== 会话缓存数据 (非持久化) =====
    /** 手动钓鱼待结算的渔场SN */
    private int pendingGroundSn = 0;

    /** 手动钓鱼待结算的鱼SN列表 */
    private List<Integer> pendingFishSns = new ArrayList<>();

    /** 手动钓鱼待消耗的鱼饵SN列表 */
    private List<List<Integer>> pendingBaits = new ArrayList<>();

    /** 手动钓鱼的次数 (1或10) */
    private int pendingCastNum = 0;

    /** 上次自动钓鱼结算的时间戳 */
    private long autoFishLastSettleTime = 0;

    // ===== Getter/Setter =====
    public HomeFish getHomeFish() {
        return homeFish;
    }

    public void setHomeFish(HomeFish homeFish) {
        this.homeFish = homeFish;
        // 重新加载数据结构
        loadDataStructure();
    }

    /**
     * 获取数据结构
     */
    public FishDataStructure getDataStructure() {
        if (dataStructure == null) {
            loadDataStructure();
        }
        return dataStructure;
    }

    /**
     * 加载数据结构
     */
    private void loadDataStructure() {
        if (homeFish == null) {
            dataStructure = null;
            return;
        }

        if (dataStructure == null) {
            dataStructure = new FishDataStructure();
        }

        dataStructure.loadFromHomeFish(homeFish);
    }

    /**
     * 保存数据结构到实体
     */
    public void saveDataStructure() {
        if (dataStructure != null && homeFish != null) {
            dataStructure.saveToHomeFish(homeFish);
        }
    }

    public int getPendingGroundSn() {
        return pendingGroundSn;
    }

    public void setPendingGroundSn(int pendingGroundSn) {
        this.pendingGroundSn = pendingGroundSn;
    }

    public List<Integer> getPendingFishSns() {
        return pendingFishSns;
    }

    public void setPendingFishSns(List<Integer> pendingFishSns) {
        this.pendingFishSns = pendingFishSns;
    }

    public List<List<Integer>> getPendingBaits() {
        return pendingBaits;
    }

    public void setPendingBaits(List<List<Integer>> pendingBaits) {
        this.pendingBaits = pendingBaits;
    }

    public int getPendingCastNum() {
        return pendingCastNum;
    }

    public void setPendingCastNum(int pendingCastNum) {
        this.pendingCastNum = pendingCastNum;
    }

    public long getAutoFishLastSettleTime() {
        return autoFishLastSettleTime;
    }

    public void setAutoFishLastSettleTime(long autoFishLastSettleTime) {
        this.autoFishLastSettleTime = autoFishLastSettleTime;
    }

    /**
     * 清空待结算缓存
     */
    public void clearPendingCache() {
        pendingGroundSn = 0;
        pendingFishSns.clear();
        pendingBaits.clear();
        pendingCastNum = 0;
    }
}
